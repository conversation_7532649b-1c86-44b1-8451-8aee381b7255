<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="369.718" height="323.745" viewBox="0 0 369.718 323.745">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_2673" data-name="Rectangle 2673" width="369.718" height="323.745" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-3">
      <rect id="Rectangle_2671" data-name="Rectangle 2671" width="95.684" height="13.338" fill="none"/>
    </clipPath>
  </defs>
  <g id="Group_4950" data-name="Group 4950" transform="translate(0)">
    <g id="Group_4949" data-name="Group 4949" transform="translate(0)" clip-path="url(#clip-path)">
      <g id="Group_4948" data-name="Group 4948">
        <g id="Group_4947" data-name="Group 4947" clip-path="url(#clip-path)">
          <path id="Path_4398" data-name="Path 4398" d="M333.243,144.464A138.443,138.443,0,1,1,194.8,6.021,138.443,138.443,0,0,1,333.243,144.464" transform="translate(26.295 2.809)" fill="#c6e2f4"/>
          <path id="Path_4399" data-name="Path 4399" d="M100.783,234.357s-.746-63.343,42.031-101.335c19.406-17.234,64.258,4.985,21.217,34.23-17.79,12.088-44.9,22.846-63.248,67.1" transform="translate(47.023 59.247)" fill="#005db9"/>
          <path id="Path_4400" data-name="Path 4400" d="M158.468,139.292S96.8,179.856,100.194,251.912" transform="translate(46.685 64.99)" fill="none" stroke="#3081f8" stroke-miterlimit="10" stroke-width="1.366"/>
          <g id="Group_4946" data-name="Group 4946" transform="translate(170.843 308.938)" opacity="0.1" style="mix-blend-mode: overlay;isolation: isolate">
            <g id="Group_4945" data-name="Group 4945">
              <g id="Group_4944" data-name="Group 4944" clip-path="url(#clip-path-3)">
                <path id="Path_4401" data-name="Path 4401" d="M212.175,217.322c0,3.683-21.419,6.668-47.841,6.668s-47.843-2.986-47.843-6.668,21.419-6.669,47.843-6.669,47.841,2.986,47.841,6.669" transform="translate(-116.491 -210.652)"/>
              </g>
            </g>
          </g>
          <path id="Path_4402" data-name="Path 4402" d="M133.546,232.368s-2.185-69.478-90.017-103.807c-30.275-11.834-74.393,31.33-11.678,49.067,25.919,7.331,62.391,9.4,101.7,54.74" transform="translate(0 59.039)" fill="#005db9"/>
          <path id="Path_4403" data-name="Path 4403" d="M20.508,141.78s89.32,23.825,113.784,110.8" transform="translate(9.569 66.151)" fill="none" stroke="#296ed3" stroke-miterlimit="10" stroke-width="1.711"/>
          <path id="Path_4404" data-name="Path 4404" d="M52.307,101.273c5.7,3.8,7.446,11.181,7.446,11.181s-7.485,1.216-13.182-2.586-9.031-8.808-7.446-11.181,7.485-1.216,13.182,2.586" transform="translate(18.071 45.47)" fill="#3087ff"/>
          <path id="Path_4405" data-name="Path 4405" d="M60.172,111.608c4.8,1.937,7.232,7.13,7.232,7.13s-5.354,2.052-10.155.114-8.04-5.13-7.232-7.13,5.353-2.052,10.155-.114" transform="translate(23.279 51.411)" fill="#3087ff"/>
          <path id="Path_4406" data-name="Path 4406" d="M63.095,109.31c1.987,4.781-.01,10.156-.01,10.156s-5.218-2.377-7.205-7.158-1.981-9.327.009-10.155,5.218,2.376,7.207,7.157" transform="translate(25.378 47.601)" fill="#3087ff"/>
          <path id="Path_4407" data-name="Path 4407" d="M66.244,118.663c6.083,1.414,9.948,7.15,9.948,7.15s-6,3.444-12.082,2.028-10.536-4.615-9.948-7.15,6-3.442,12.082-2.028" transform="translate(25.247 55.054)" fill="#3087ff"/>
          <path id="Path_4408" data-name="Path 4408" d="M72.86,117.19c1.747,6-1.361,12.174-1.361,12.174s-5.94-3.543-7.686-9.539-1.137-11.447,1.361-12.174,5.94,3.543,7.686,9.539" transform="translate(29.309 50.19)" fill="#3087ff"/>
          <path id="Path_4409" data-name="Path 4409" d="M69.929,127.024C76.174,127,81.253,131.7,81.253,131.7s-5.046,4.73-11.291,4.75-11.315-2.071-11.323-4.673,5.046-4.728,11.291-4.75" transform="translate(27.359 59.266)" fill="#3087ff"/>
          <path id="Path_4410" data-name="Path 4410" d="M75.325,136.495c6.658-.309,12.288,4.464,12.288,4.464s-5.164,5.272-11.822,5.582-12.159-1.689-12.288-4.463,5.164-5.274,11.822-5.583" transform="translate(29.628 63.678)" fill="#3087ff"/>
          <path id="Path_4411" data-name="Path 4411" d="M79.882,146.006c6.592-.986,12.679,3.19,12.679,3.19s-4.6,5.771-11.193,6.757-12.268-.443-12.679-3.19,4.6-5.771,11.193-6.757" transform="translate(32.038 68.052)" fill="#3087ff"/>
          <path id="Path_4412" data-name="Path 4412" d="M85.557,156.737c6.664.111,11.982,5.23,11.982,5.23s-5.486,4.936-12.151,4.825-12.029-2.452-11.982-5.228,5.486-4.938,12.151-4.827" transform="translate(34.249 73.128)" fill="#3087ff"/>
          <path id="Path_4413" data-name="Path 4413" d="M74.116,124.28c-2.462,5.739-.129,12.249-.129,12.249s6.327-2.794,8.789-8.534,2.52-11.224.128-12.249-6.325,2.8-8.788,8.534" transform="translate(34.054 53.926)" fill="#3087ff"/>
          <path id="Path_4414" data-name="Path 4414" d="M82.52,133.282c-3.774,5.494-2.687,12.794-2.687,12.794s7.2-1.607,10.977-7.1,4.975-11.224,2.685-12.8-7.2,1.609-10.976,7.1" transform="translate(37.18 58.685)" fill="#3087ff"/>
          <path id="Path_4415" data-name="Path 4415" d="M88.427,141.995c-4.313,5.082-3.976,12.456-3.976,12.456s7.33-.867,11.643-5.948,6.091-10.659,3.973-12.456-7.33.867-11.64,5.948" transform="translate(39.4 63.214)" fill="#3087ff"/>
          <path id="Path_4416" data-name="Path 4416" d="M91.566,154.212c-3.419,5.721-1.874,12.94-1.874,12.94s7.088-2.059,10.507-7.782,4.259-11.514,1.874-12.938-7.088,2.059-10.507,7.78" transform="translate(41.686 68.173)" fill="#3087ff"/>
          <path id="Path_4417" data-name="Path 4417" d="M90.409,168.273c6.5,1.483,10.647,7.588,10.647,7.588s-6.387,3.7-12.885,2.216-11.265-4.881-10.647-7.588,6.387-3.7,12.885-2.216" transform="translate(36.146 78.191)" fill="#3087ff"/>
          <path id="Path_4418" data-name="Path 4418" d="M97.748,166.889c-4.524,4.894-4.5,12.275-4.5,12.275s7.359-.554,11.885-5.447,6.539-10.389,4.5-12.275-7.359.553-11.885,5.447" transform="translate(43.507 75.027)" fill="#3087ff"/>
          <path id="Path_4419" data-name="Path 4419" d="M93.362,180.747c6.349,2.031,9.962,8.467,9.962,8.467s-6.679,3.143-13.026,1.112-10.809-5.821-9.962-8.467,6.679-3.143,13.026-1.112" transform="translate(37.435 83.749)" fill="#3087ff"/>
          <path id="Path_4420" data-name="Path 4420" d="M101.194,179.776c-4.925,4.491-5.529,11.847-5.529,11.847s7.38.075,12.3-4.416,7.4-9.8,5.53-11.847-7.381-.075-12.306,4.416" transform="translate(44.635 81.44)" fill="#3087ff"/>
          <path id="Path_4421" data-name="Path 4421" d="M48.407,104.77s32.706,17.712,54.523,67.191,17.655,96.273,17.655,96.273" transform="translate(22.586 48.883)" fill="none" stroke="#3087ff" stroke-miterlimit="10" stroke-width="1.283"/>
          <path id="Path_4422" data-name="Path 4422" d="M247.567,143.612c-4.237,2.15-6.051,7.089-6.051,7.089s5.057,1.455,9.3-.7,6.946-5.324,6.051-7.089-5.057-1.455-9.3.7" transform="translate(112.685 66.147)" fill="#78b0ff"/>
          <path id="Path_4423" data-name="Path 4423" d="M240.358,150.1a10.818,10.818,0,0,0-5.57,4.316,10.813,10.813,0,0,0,6.987.915c3.467-.939,5.96-2.872,5.57-4.316s-3.52-1.854-6.987-.915" transform="translate(109.546 69.797)" fill="#78b0ff"/>
          <path id="Path_4424" data-name="Path 4424" d="M236.914,148.216a10.815,10.815,0,0,0-.83,7,10.815,10.815,0,0,0,5.552-4.337c1.763-3.13,2.135-6.262.83-7s-3.79,1.207-5.552,4.337" transform="translate(110.036 67.058)" fill="#78b0ff"/>
          <path id="Path_4425" data-name="Path 4425" d="M236.575,154.547c-4.307.474-7.441,4.106-7.441,4.106s3.848,2.866,8.156,2.392,7.638-2.31,7.441-4.1-3.848-2.866-8.156-2.393" transform="translate(106.908 72.056)" fill="#78b0ff"/>
          <path id="Path_4426" data-name="Path 4426" d="M229.793,152.86c-1.7,3.986-.066,8.5-.066,8.5s4.384-1.951,6.082-5.938,1.726-7.792.065-8.5-4.384,1.952-6.08,5.938" transform="translate(106.856 68.497)" fill="#78b0ff"/>
          <path id="Path_4427" data-name="Path 4427" d="M233.382,160.113c-4.3-.529-8.186,2.286-8.186,2.286s3.087,3.674,7.387,4.2,7.966-.494,8.186-2.286-3.087-3.674-7.387-4.2" transform="translate(105.071 74.674)" fill="#78b0ff"/>
          <path id="Path_4428" data-name="Path 4428" d="M229.208,166.223c-4.561-.761-8.833,2.062-8.833,2.062s3.122,4.059,7.683,4.821,8.516-.161,8.835-2.062-3.124-4.059-7.685-4.821" transform="translate(102.822 77.495)" fill="#78b0ff"/>
          <path id="Path_4429" data-name="Path 4429" d="M225.207,172.424c-4.46-1.222-9,1.153-9,1.153s2.694,4.356,7.154,5.577,8.489.705,9-1.153-2.694-4.356-7.154-5.577" transform="translate(100.878 80.286)" fill="#78b0ff"/>
          <path id="Path_4430" data-name="Path 4430" d="M220.516,179.312c-4.6-.472-8.687,2.615-8.687,2.615s3.373,3.854,7.974,4.326,8.489-.7,8.685-2.615-3.372-3.854-7.972-4.326" transform="translate(98.834 83.639)" fill="#78b0ff"/>
          <path id="Path_4431" data-name="Path 4431" d="M228.34,157.8c1.223,4.156-.921,8.449-.921,8.449s-4.128-2.446-5.352-6.6-.811-7.94.921-8.45,4.128,2.448,5.352,6.6" transform="translate(103.283 70.519)" fill="#78b0ff"/>
          <path id="Path_4432" data-name="Path 4432" d="M222.494,163.4c2.146,4.1.8,9.036.8,9.036s-4.831-1.7-6.977-5.8-2.5-8.141-.8-9.036,4.829,1.7,6.976,5.8" transform="translate(100.051 73.451)" fill="#78b0ff"/>
          <path id="Path_4433" data-name="Path 4433" d="M218.033,168.954c2.552,3.857,1.712,8.908,1.712,8.908s-4.979-1.2-7.531-5.057-3.317-7.845-1.711-8.908,4.979,1.2,7.531,5.057" transform="translate(97.823 76.348)" fill="#78b0ff"/>
          <path id="Path_4434" data-name="Path 4434" d="M214.354,177.049c1.885,4.224.224,9.068.224,9.068s-4.714-2-6.6-6.226-1.984-8.283-.224-9.068,4.714,2,6.6,6.226" transform="translate(96.348 79.64)" fill="#78b0ff"/>
          <path id="Path_4435" data-name="Path 4435" d="M216.181,186.748c-4.6.487-7.959,4.35-7.959,4.35s4.095,3.075,8.692,2.588,8.163-2.433,7.961-4.35-4.095-3.075-8.694-2.588" transform="translate(97.151 87.08)" fill="#78b0ff"/>
          <path id="Path_4436" data-name="Path 4436" d="M209.7,185.353c2.715,3.744,2.09,8.827,2.09,8.827s-5.026-.988-7.739-4.733-3.65-7.7-2.09-8.826,5.024.987,7.739,4.731" transform="translate(93.879 84.132)" fill="#78b0ff"/>
          <path id="Path_4437" data-name="Path 4437" d="M213.049,195.057c-4.541.876-7.56,5.011-7.56,5.011s4.341,2.716,8.882,1.841,7.925-3.119,7.562-5.011-4.343-2.716-8.883-1.841" transform="translate(95.876 90.846)" fill="#78b0ff"/>
          <path id="Path_4438" data-name="Path 4438" d="M206.54,193.978c3.023,3.5,2.833,8.618,2.833,8.618s-5.092-.557-8.115-4.057-4.291-7.358-2.832-8.618,5.09.557,8.113,4.057" transform="translate(92.298 88.426)" fill="#78b0ff"/>
          <path id="Path_4439" data-name="Path 4439" d="M265.069,146.238s-23.992,9.506-43.1,41.8-20.1,64.868-20.1,64.868" transform="translate(94.189 68.231)" fill="none" stroke="#78b0ff" stroke-miterlimit="10" stroke-width="0.89"/>
          <path id="Path_4440" data-name="Path 4440" d="M199.054,249.626s-24.583-75.43,12.338-136.84c16.748-27.856,78.671-18.074,38.162,32.977-16.742,21.1-45.122,44.1-50.5,103.863" transform="translate(89.133 45.493)" fill="#005db9"/>
          <path id="Path_4441" data-name="Path 4441" d="M232.1,113.808s-58.534,71.547-27.535,156.406" transform="translate(91.222 53.1)" fill="none" stroke="#296ed3" stroke-miterlimit="10" stroke-width="1.711"/>
          <path id="Path_4442" data-name="Path 4442" d="M204.955,226.6s11.37-36.443,42.963-50.249c14.33-6.262,35.9,14.826,5.783,23.571-12.447,3.614-29.987,4.73-48.746,26.678" transform="translate(95.627 81.747)" fill="#2d76e3"/>
          <path id="Path_4443" data-name="Path 4443" d="M256.008,182.626s-42.9,11.763-54.375,53.691" transform="translate(94.077 85.209)" fill="none" stroke="#296ed3" stroke-miterlimit="10" stroke-width="0.823"/>
          <path id="Path_4444" data-name="Path 4444" d="M184.525,31.058l-.169-8.685a5.045,5.045,0,0,0-1.684-3.65c-1.687-1.515-3.766-4.215-3.281-4.846.9-1.17,3.842,3.108,5.03,2.231.353-.261-1.67-11.957-.469-11.884.936.059,1.3,6.644,1.719,6.594S185.981-.085,186.839,0c.829.082,1.126,9.962,1.7,9.6.837-.528-.025-8.681,1.361-9.074.93-.264.789,10.377,1.308,10.562.648.23.928-6.883,1.732-6.6,1.109.39-.563,12.492-.563,12.492l-2.109,15.37Z" transform="translate(83.666 0)" fill="#ffb49b"/>
          <path id="Path_4445" data-name="Path 4445" d="M104.984,54.247l-5.85-6.421a5.046,5.046,0,0,1-1.286-3.81c.182-2.259-.164-5.648-.95-5.774-1.458-.232-.653,4.9-2.118,5.079-.438.054-7.01-9.829-7.831-8.951-.639.685,3.622,5.718,3.285,5.969s-7.72-7.7-8.285-7.051c-.546.631,6.032,8.008,5.368,8.138-.971.194-5.95-6.318-7.227-5.651-.856.447,6.563,8.076,6.314,8.568-.312.613-5.407-4.357-5.8-3.6-.538,1.046,9,8.682,9,8.682l12.1,9.709Z" transform="translate(37.335 15.514)" fill="#ffb49b"/>
          <path id="Path_4446" data-name="Path 4446" d="M173.63,38.927a26.077,26.077,0,0,0-25.335-19.962,25.4,25.4,0,0,0-3.116.192,56.546,56.546,0,0,1-14.519.132c-2.134-.287-4.516-.6-6.968-.922a16.057,16.057,0,0,0-17.379,20.865c.045.139.091.277.138.415A61,61,0,0,1,109.6,58.585q.011.807.07,1.625a26.086,26.086,0,0,0,27.161,24.1,47.4,47.4,0,0,1,20.4,3.335,22.317,22.317,0,0,0,7.917,2.025,26.076,26.076,0,0,0,8.477-50.741" transform="translate(49.222 8.504)" fill="#004890"/>
          <path id="Path_4447" data-name="Path 4447" d="M210.474,35.507S206.428,75.88,200.255,87.64c-4.876,9.291-29.31,30.04-29.31,30.04a8.308,8.308,0,0,1-13.107-10.015s22.283-24.03,25.592-34.179c3.137-9.622-.5-40.482-.5-40.482a13.9,13.9,0,1,1,27.545,2.5" transform="translate(73.108 8.829)" fill="#fff"/>
          <path id="Path_4448" data-name="Path 4448" d="M175.671,184.791c-4.545-1.509-5.4,1.672-6.919,7.327s-1.8,9.935-.345,10.328c1.359.367,3.3-1.631,6.559-6.5,3.913-5.844,3.664-10.174.705-11.156" transform="translate(78.127 86.044)" fill="#41378c"/>
          <path id="Path_4449" data-name="Path 4449" d="M177.176,180.8l-4.411,6.113c-1.411,1.914-1.357,3.3-.485,4.161a2.322,2.322,0,0,0,3.191.066L182.795,185Z" transform="translate(80.092 84.355)" fill="#ffb49b"/>
          <path id="Path_4450" data-name="Path 4450" d="M169.178,109.5c-22.2,8.744-16.062,38.814-5.6,45.021,15.552,9.226,46.574,5.353,46.6,7.757.106,10.133-26.948,52.665-26.948,52.665a1.116,1.116,0,0,0,.123,1.267A21.889,21.889,0,0,0,188.815,221a.932.932,0,0,0,1.079-.239c15.157-14.117,45.929-48.988,49.551-64.015,6.044-25.073-66.118-48.877-70.268-47.243" transform="translate(71.913 51.053)" fill="#004890"/>
          <path id="Path_4451" data-name="Path 4451" d="M96,174.686c-1.951-4.372-4.873-2.854-10.075-.164s-8.55,5.371-7.856,6.713c.647,1.251,3.43,1.336,9.22.458,6.955-1.054,9.982-4.159,8.711-7.006" transform="translate(36.383 80.219)" fill="#41378c"/>
          <path id="Path_4452" data-name="Path 4452" d="M98.188,172.943l-7.488.867c-2.364.249-3.351,1.226-3.4,2.449a2.32,2.32,0,0,0,2.1,2.4l9.467,1.27Z" transform="translate(40.732 80.691)" fill="#ffb49b"/>
          <path id="Path_4453" data-name="Path 4453" d="M199.33,112.636c-4.546-4.695-26.126-.966-26.126-.966-22.34,8.38-21.215,27.847-18.74,39.753,3.68,17.706-1.986,32.483-3.744,34.124-7.408,6.913-57.056,15.635-57.056,15.635a1.119,1.119,0,0,0-.852.946,21.9,21.9,0,0,0,.154,7.264c.067.39.509.6.9.635,20.645,1.665,62.221,5.3,75.473-2.656,30.36-18.23,47.268-76.89,29.986-94.735" transform="translate(43.196 51.447)" fill="#ffd51d"/>
          <path id="Path_4454" data-name="Path 4454" d="M177.513,141.08A17.772,17.772,0,0,0,192.323,128.6c3.037-10.083,7.189-26.538,5.3-35.96-1.766-8.8-8.566-25.111-25.63-25.108-11.379,0-34.349,3.379-32.845,20.843.937,10.886,2.8,31.367,11.356,45.075a12.9,12.9,0,0,0,6.93,5.453,47.427,47.427,0,0,0,20.082,2.178" transform="translate(64.889 31.508)" fill="#fff"/>
          <path id="Path_4455" data-name="Path 4455" d="M162.05,82.155h0a5.971,5.971,0,0,1-5.97-5.97v-19.7h11.941v19.7a5.971,5.971,0,0,1-5.97,5.97" transform="translate(72.823 26.354)" fill="#ffa587"/>
          <path id="Path_4456" data-name="Path 4456" d="M166.826,71.818h0a7.524,7.524,0,0,0,1.194-.1V56.483H156.079v2.223c0,5.935,4.812,13.111,10.747,13.111" transform="translate(72.823 26.354)" fill="#f5876e"/>
          <path id="Path_4457" data-name="Path 4457" d="M171.424,72.445h0c-4.881,1.867-13.688-1.53-15.554-6.412,0,0-4.071-7.389-3.854-10.075,1.009-12.416,12.852-10.015,17.172-8.085,2.462,1.1,3.668,8.794,4.36,11.4,1.34,5.051.012,12.356-2.124,13.173" transform="translate(70.923 21.605)" fill="#ffb49b"/>
          <path id="Path_4458" data-name="Path 4458" d="M151.042,59.346a4.2,4.2,0,0,0,5.413,2.457l-3.021-7.9a4.2,4.2,0,0,0-2.392,5.442" transform="translate(70.34 25.15)" fill="#ffa587"/>
          <path id="Path_4459" data-name="Path 4459" d="M168.644,47.373l-14.9,5.7L155,56.348a3.117,3.117,0,0,0-.953,5,4.745,4.745,0,0,0,.82-.934,4.148,4.148,0,0,1,2.014-1.565,8.157,8.157,0,0,0,4.1-3.515,9.016,9.016,0,0,1,5.841-4.356,10.353,10.353,0,0,0,1.555-.458,11.378,11.378,0,0,0,1.245-.585Z" transform="translate(71.466 22.103)" fill="#ffa587"/>
          <path id="Path_4460" data-name="Path 4460" d="M162.438,34.074a11.666,11.666,0,0,0-6.561,6.975,14.791,14.791,0,0,1-6.381,7.5l-.1.065a9.448,9.448,0,0,0-4.164,9.587,10.072,10.072,0,0,0,.373,1.412,10.325,10.325,0,0,1,.607,4.659,4.567,4.567,0,0,0,.263,2.212,5.08,5.08,0,0,0,6.644,2.713,5.376,5.376,0,0,0,2.6-2.09,4.744,4.744,0,0,1,2.3-1.782,9.3,9.3,0,0,0,4.678-4.007,10.272,10.272,0,0,1,6.655-4.966,11.717,11.717,0,0,0,8.4-15.968,12.082,12.082,0,0,0-15.317-6.315" transform="translate(67.689 15.514)" fill="#004890"/>
          <path id="Path_4461" data-name="Path 4461" d="M93.4,64.411s26.588,24.69,38.641,29.842c8.716,3.725,42.573,7.3,42.573,7.3a7.8,7.8,0,0,0,9.515-6.135,7.281,7.281,0,0,0-3.685-7.777s-30.063-4.859-38.4-9.64c-8.436-4.837-28.007-28.513-28.007-28.513-7.59-7.3-13.554-8.258-19.591-2.622C89.371,51.6,87.316,60.636,93.4,64.411" transform="translate(41.832 20.171)" fill="#fff"/>
        </g>
      </g>
    </g>
  </g>
</svg>
