import React, {useState, useEffect} from "react";
import {Tab, TabContainer} from "../../Tabs/Tabs";
import {InteractionType} from "@azure/msal-browser";
import {formBuilderApiRequest} from "../../../../utilities/Msal/msalConfig";
import {DataTable} from "primereact/datatable";
import {Column} from "primereact/column";
// Icons
import Pending from "../../../../public/Icons/Pending.svg";
import Signature from "../../../../public/Icons/Signature_white.svg";
// Hooks
import {useAccount, useMsal, useMsalAuthentication} from "@azure/msal-react";
import {useApi} from "../../../../hooks/useApi";
import {formatDateTime} from "../../../../utilities/DateTimeConverter/DateTimeConverter";

const api = process.env.NEXT_PUBLIC_FORM_BUILDER_API_DEMO;

export const MySignatures = () => {
  const headerStyle = {fontWeight: "600", fontSize: "15.5px", color: "#000"};
  const {accounts} = useMsal();
  const {loading, callApi} = useApi();
  const account = useAccount(accounts[0] ?? {});
  const {acquireToken} = useMsalAuthentication(
    InteractionType.Silent,
    formBuilderApiRequest
  );

  const convertDate = (date) => {
    return formatDateTime(date);
  };

  const [activeTab, setActiveTab] = useState("Pending");
  const [signatures, setSignatures] = useState([]);

  const tabMapper = {
    Pending: 2,
    Signed: 3,
  };

  useEffect(() => {
    const getSignatures = async () => {
      const token = await acquireToken();
      const url = `${api}/ESignRequest/Filter?recepientEmail=${account.username}&requestStatus=${tabMapper[activeTab]}`;
      const response = await callApi({url, token});
      setSignatures(response?.data?.requests);
    };
    getSignatures();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTab]);

  const handleRowClick = (value) => {
    const baseUrl = "https://demo.papyrrus.com/form-builder-studio/esignature/";
    const {
      requestType,
      eSignRequestId,
      firstName,
      lastName,
      email,
      emailSubject,
      emailMessage,
      draftStep,
      salutationId,
      sequenceOrder,
      sequenceFlag,
      updatedBy,
      requestMappingDTOs,
    } = value;
    const {fileId, requestMapId} = requestMappingDTOs[0];

    const path = value?.requestStatus === 2 ? "signOne" : "signTwo";
    const documentSigned = value?.requestStatus === 2 ? "false" : "true";

    const url = `${baseUrl}${path}?fileId=${fileId}&requestMapId=${requestMapId}&requestType=${requestType}&eSignRequestId=${eSignRequestId}&firstName=${firstName}&lastName=${lastName}&email=${email}&emailSubject=${emailSubject}&emailMessage=${emailMessage}&draftStep=${draftStep}&documentSigned=${documentSigned}&salutationId=${salutationId}&sequenceFlag=${sequenceFlag}&sequenceOrder=${sequenceOrder}&userId=${updatedBy}`;

    window.open(url, "_blank");
  };

  return (
    <>
      <TabContainer>
        <Tab
          title="Pending"
          isActive={activeTab === "Pending"}
          handleClick={() => setActiveTab("Pending")}
          display={true}
          icon={Pending}
        />
        <Tab
          title="Signed"
          isActive={activeTab === "Signed"}
          handleClick={() => setActiveTab("Signed")}
          display={true}
          icon={Signature}
        />
      </TabContainer>
      <DataTable
        value={signatures}
        className="p-datatable-sm"
        paginator
        rows={10}
        totalRecords={signatures?.length}
        emptyMessage="No records found"
        loading={loading}
        selectionMode="single"
        onSelectionChange={(e) => handleRowClick(e.value)}
      >
        <Column
          className="dashboardTitle"
          field="emailSubject"
          header="Subject"
          sortable
          headerStyle={{...headerStyle, width: "20%"}}
        />
        <Column
          className="dashboardTitle"
          field="email"
          header="Recipients"
          sortable
          headerStyle={{...headerStyle, width: "20%"}}
        />
        <Column
          className="dashboardTitle"
          field="createdTime"
          header="Sent On"
          body={(rowData) => convertDate(rowData.createdTime)}
          sortable
          headerStyle={{...headerStyle, width: "20%"}}
        />
        <Column
          className="dashboardTitle"
          field="lastUpdated"
          body={(rowData) => convertDate(rowData.lastUpdated)}
          header="Last Updated"
          sortable
          headerStyle={{...headerStyle, width: "20%"}}
        />
      </DataTable>
    </>
  );
};
