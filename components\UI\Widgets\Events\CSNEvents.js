import React, { useState } from 'react'
import styles from "./Events.module.css";
import { Calendar } from 'primereact/calendar'
import { formatDateForHeader, formatTime } from "../../../../utilities/DateTimeConverter/DateTimeConverter";

export const CSNEvents = ({ dummyEventsData, group}) => {
  const [selectedDate, setSelectedDate] = useState(new Date(new Date().setHours(0, 0, 0, 0)));

  function groupEventsByDate(events) {
    const groups = {};
    events.forEach(event => {
      const dateKey = formatDateForHeader(event.eventDate);
      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      groups[dateKey].push(event);
    });
    return groups;
  }

  function isEventInRange(eventDate, rangeStart, rangeEnd) {
    const event = new Date(eventDate);
    return event >= rangeStart && event <= rangeEnd;
  }

  function renderEvents() {
    const rangeEnd = new Date(selectedDate);
    rangeEnd.setDate(rangeEnd.getDate() + 7);

    const filteredEvents = dummyEventsData.filter(event => {
      const eventDate = new Date(event.eventDate);
      return event.group === group && isEventInRange(eventDate, selectedDate, rangeEnd);
    });
    const groupedEvents = groupEventsByDate(filteredEvents);

    if (Object.keys(groupedEvents).length === 0) {
      return <div>No events found for the selected period.</div>;
    }

    return Object.keys(groupedEvents).map((dateKey, index) => (
      <div key={index}>
        <div className={styles.dateHeader}>{dateKey}</div>
        {groupedEvents[dateKey].map((event, eventIndex) => (
          <div key={eventIndex} className={styles.appointmentContainer}>
            <div className={styles.leftContainer}>
              <div className={styles.eventTime}>{formatTime(event.eventStartTime)} - {formatTime(event.eventEndTime)}</div>
              <div><strong>Event Name:</strong> {event.eventName}</div>
              <div><strong>Description:</strong> {event.description}</div>
            </div>
          </div>
        ))}
      </div>
    ));
  }

  const handleClick = () => {
    window.open("https://www.csn.edu/search?f.Tabs%7C1a3f58ca-a034-4614-97e6-56b30d710b01%7Eds-csn-events-redesign=Events&profile=csn-he-redesign&query=%21nullquery&collection=1a3f58ca-a034-4614-97e6-56b30d710b01%7Esp-csn-higher-education-redesign");
  };

return (
  <div style={{display: 'flex', flexDirection: 'column'}}>
    <Calendar
      style={{width: '100%', height: '400px'}}
      inline
      showIcon
      value={selectedDate}
      onChange={(e) => setSelectedDate(new Date(e.value.setHours(0, 0, 0, 0)))}
    />
    <div className={styles.eventContainer}>
      {renderEvents()}
    </div>
    <div className={styles.viewLink} onClick={handleClick}>View All</div>
  </div>
);
}