import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";

export const Invoice = ({}) => {
  const mockData = [
    { 
      id: 98665,
      title: 'PeopleSoft RECONNECT ticket',
      totalAmount: 1499.99,
      department: 'Registrar',
      lastUpdated: '7/29/2024',
    },
    { 
      id: 98666,
      title: 'Software Licenses',
      totalAmount: 999.88,
      department: 'Registrar',
      lastUpdated: '7/31/2024',
    },
    { 
      id: 98667,
      title: 'Office Supplies',
      totalAmount: 200.45,
      department: 'Human Resources',
      lastUpdated: '8/01/2024',
    },
    { 
      id: 98668,
      title: 'Consulting Fees',
      totalAmount: 3200.00,
      department: 'IT',
      lastUpdated: '8/03/2024',
    },
    { 
      id: 98669,
      title: 'Employee Training',
      totalAmount: 800.00,
      department: 'Training',
      lastUpdated: '8/05/2024',
    },
    { 
      id: 98670,
      title: 'Travel Expenses',
      totalAmount: 450.75,
      department: 'Sales',
      lastUpdated: '8/07/2024',
    },
    { 
      id: 98671,
      title: 'Event Sponsorship',
      totalAmount: 10000.00,
      department: 'Marketing',
      lastUpdated: '8/10/2024',
    },
    { 
      id: 98672,
      title: 'New Computers',
      totalAmount: 5000.50,
      department: 'IT',
      lastUpdated: '8/12/2024',
    },
    { 
      id: 98673,
      title: 'Conference Fees',
      totalAmount: 250.00,
      department: 'Research',
      lastUpdated: '8/15/2024',
    },
    { 
      id: 98674,
      title: 'Advertising',
      totalAmount: 1500.00,
      department: 'Marketing',
      lastUpdated: '8/17/2024',
    },
];

  return (
    <DataTable
      value={mockData}
      lazy
      columnResizeMode="expand"
      dataKey="id"
      paginator
      first={0}
      rows={10}
      globalFilterFields={[]}
      selectionMode="single"
      >
      <Column
        field="id"
        header="Invoice ID"
      />
      <Column
        field="title"
        header="Title"
      />
      <Column
        field="totalAmount"
        header="Total Amount"
      />
      <Column
        field="department"
        header="Department"
      />
      <Column
        field="lastUpdated"
        header="Last Updated"
      />
    </DataTable>
  );
}