import moment from "moment/moment"
import timeZone from 'moment-timezone';

export function dateFormat(date) {
    // return `${new Intl.DateTimeFormat('en-us', { day: '2-digit', month: 'short', year: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric', timeZone: 'America/Los_Angeles', timeZoneName: 'short', hourCycle: 'h12' }).format(new Date(date))}`
    return `${moment(date).tz("America/Los_Angeles").format('DD-MMM-YYYY, HH:mm:ss a')} ${timeZone().tz("America/Los_Angeles").format('z')}`
}

export const toDateObject = (date) => {
    if (typeof date !== 'string' && !isNaN(Date.parse(date))) {
        return new Date(date)
    } else if (date instanceof Date && !isNaN(date)) {
        return date
    } else if (typeof date === 'string') {
        return new Date(date)
    } else {
        console.log('Unknown date format for toDateObject ', date)
        return null
    }
}

export const formatDateFromString = (dateString) => {
    const newDate = new Date(dateString)
    return `${moment(newDate).format('MM/DD/YYYY')}`
}

export const formatToTimeStampFromDate = (date) => {
    return `${moment(date).format('MM/DD/YYYY, hh:mm:ss A')}`
}

