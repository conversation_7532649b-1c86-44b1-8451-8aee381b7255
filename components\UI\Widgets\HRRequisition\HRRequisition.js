import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";

export const HRRequisition = ({}) => {
  const mockData = [
    { 
      id: 123,
      title: 'Data Analyst II',
      department: 'Registrar', 
      lastUpdated: '7/28/2024',  
    },
    { 
      id: 124,
      title: 'Data Analyst I',
      department: 'Registrar', 
      lastUpdated: '7/29/2024',  
    },
    { 
      id: 125,
      title: 'HR Manager',
      department: 'Human Resources', 
      lastUpdated: '7/30/2024',  
    },
    { 
      id: 126,
      title: 'IT Support Specialist',
      department: 'IT', 
      lastUpdated: '7/31/2024',  
    },
    { 
      id: 127,
      title: 'Marketing Coordinator',
      department: 'Marketing', 
      lastUpdated: '8/01/2024',  
    },
    { 
      id: 128,
      title: 'Sales Executive',
      department: 'Sales', 
      lastUpdated: '8/02/2024',  
    },
    { 
      id: 129,
      title: 'Training Specialist',
      department: 'Training', 
      lastUpdated: '8/03/2024',  
    },
  ];
  
  return (
    <DataTable
      value={mockData}
      lazy
      columnResizeMode="expand"
      dataKey="id"
      paginator
      first={0}
      rows={10}
      globalFilterFields={[]}
      selectionMode="single"
      >
      <Column
        field="id"
        header="Req ID"
      />
      <Column
        field="title"
        header="Title"
      />
      <Column
        field="department"
        header="Department"
      />
      <Column
        field="lastUpdated"
        header="Last Updated"
      />
    </DataTable>
  );
}