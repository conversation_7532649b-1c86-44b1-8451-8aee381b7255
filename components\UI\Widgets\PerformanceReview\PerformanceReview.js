import ReactDOM from "react-dom";
import { Doughnut } from "react-chartjs-2";
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from "chart.js";
import { <PERSON><PERSON> } from "primereact/button";
import { Line } from "react-chartjs-2";
import {
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
} from "chart.js";
import React, { useState, useEffect } from "react";
import styles from "../../../WidgetDashboard/WidgetDashboard.module.css";
import { formBuilderApiRequest } from "../../../../utilities/Msal/msalConfig";
import { useAccount, useMsal, useMsalAuthentication } from "@azure/msal-react";
import { useApi } from "../../../../hooks/useApi";
import useUtilityFunctions from "../../../../hooks/useUtilityFunctions";
import { InteractionType } from "@azure/msal-browser";
import { useDashboard } from "../../../../hooks/useDashboard";

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

ChartJS.register(ArcElement, Tooltip, Legend);
const api = process.env.NEXT_PUBLIC_FORM_BUILDER_API_DEMO;

export const PerformanceReview = ({userName}) => {
  const { accounts } = useMsal();
  const { loading, callApi } = useApi();
  const account = useAccount(accounts[0] ?? {});
  const { createDashboardDate } = useUtilityFunctions();
  const { acquireToken } = useMsalAuthentication(
    InteractionType.Silent,
    formBuilderApiRequest
  );

  const dataForJohn = {
    labels: [
      "Pending",
      "Supervisor Review",
      "Dep Head Review",
      "HR Review",
      "Completed",
    ],
    datasets: [
      {
        data: [
          8,
          3,
          5,
          4,
          9,
        ],
        backgroundColor: [
          "#ff8fad",
          "#a2e6f9",
          "#fa8cff",
          "#09e9e4",
          "#fbe69e",
        ],
        borderColor: ["#fff", "#fff", "#fff", "#fff", "#fff"],
        borderWidth: 1,
      },
    ],
  };
  
  const dataForBarkly = {
    labels: [
      "Pending",
      "Supervisor Review",
      "Dep Head Review",
      "HR Review",
      "Completed",
    ],
    datasets: [
      {
        data: [
          9,
          2,
          5,
          5,
          3,
        ],
        backgroundColor: [
          "#ff8fad",
          "#a2e6f9",
          "#fa8cff",
          "#09e9e4",
          "#fbe69e",
        ],
        borderColor: ["#fff", "#fff", "#fff", "#fff", "#fff"],
        borderWidth: 1,
      },
    ],
  };
  
  // Determine the data based on the username
  const defaultData = userName?.toLowerCase() === "john keating" ? dataForJohn : dataForBarkly;
  console.log(defaultData);
  console.log(userName);
  const initialLazyParams = {
    page: 0,
    rows: 30,
    sortField: "lastUpdatedAtUtc",
    sortOrder: -1,
    solutionId: 2,
  };

  const {
    rows,
    setRows,
    totalCount,
    setTotalCount,
    lazyParams,
    globalFilter,
    lazyParamsToQueryString,
    onSort,
    onPage,
    onFilter,
    onGlobalFilterChange,
  } = useDashboard({ initialLazyParams });

  const [teamReview, setTeamReview] = useState(0);
  const [yourReview, setYourReview] = useState(0);
  const [processedData, setProcessedData] = useState({
    labels: [],
    datasets: [
      {
        data: [],
        backgroundColor: [],
        borderColor: [],
        borderWidth: 1,
      },
    ],
  });

  useEffect(() => {
    const loadAllData = async () => {
      if (account) {
        try {
          const { accessToken } = await acquireToken();
          const queryString = lazyParamsToQueryString(lazyParams);

          // Define all base URLs
          const urlBaseMapper = {
            InProgress: `FormSubmission/filter/status/2/finalized/false`,
            Approved: `FormSubmission/filter/status/3/finalized/true`,
            Rejected: `FormSubmission/filter/status/4/finalized/true`,
          };

          const apiCalls = Object.entries(urlBaseMapper).map(
            async ([tabName, baseUrl]) => {
              const userFormApprovalUrl = `${api}${baseUrl}${queryString}`;

              const formSubmissionParams = {
                method: "GET",
                url: userFormApprovalUrl,
                headers: {
                  Accept: "*/*",
                  Authorization: `Bearer ${accessToken}`,
                  "next-action": "userFormApproval",
                },
              };

              const result = await callApi(formSubmissionParams);
              return { tabName, data: result?.data };
            }
          );

          const results = await Promise.all(apiCalls);
          const processedData = processData(results);
          setProcessedData(processedData);
        } catch (error) {
          console.error("Error loading data:", error);
        }
      }
    };

    loadAllData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [lazyParams, acquireToken, account]);

  // Fetch Rating
  useEffect(() => {
    const fetchRating = async () => {
      if (account) {
        const { accessToken } = await acquireToken();
        const pieChartUrl = `${api}/PerformanceReview/MyPerformancePageValues`;

        const pieChartParams = {
          method: "GET",
          url: pieChartUrl,
          headers: {
            Accept: "*/*",
            Authorization: `Bearer ${accessToken}`,
          },
        };
        const result = await callApi(pieChartParams);
        if (result?.data) {
          const averageRating =
            result?.data?.performanceReviewStats?.averageRating || 0;
          setYourReview(averageRating);
        }
      }
    };

    fetchRating();
  }, [acquireToken, account]);

  const processData = (data) => {
    if (!data.length) return { labels: [], counts: [] };

    const aggregatedData = {
      Pending: 0,
      SupervisorReview: 0,
      DepHeadReview: 0,
      HRReview: 0,
      Completed: 0,
    };

    let totalRating = 0; // Total of all ratings
    let ratingCount = 0; // Number of rated submissions

    data.forEach((record) => {
      const formStatus = record?.tabName || "Unknown";
      const formCount = record?.data?.count || 0;
      const formSubmissionData = record?.data?.formSubmissions;

      if (formStatus === "InProgress") {
        aggregatedData.Pending += formCount;
        if (formSubmissionData?.length > 0) {
          formSubmissionData.forEach((submission) => {
            if (submission?.currentApproverGroupName.includes("HR Team")) {
              aggregatedData.HRReview += 1; // Correct increment
            } else if (
              submission?.currentApproverGroupName.includes("$Supervisor1")
            ) {
              aggregatedData.SupervisorReview += 1; // Correct increment
            } else if (
              submission?.currentApproverGroupName.includes("$Supervisor2")
            ) {
              aggregatedData.DepHeadReview += 1; // Correct increment
            }
          });
        }
      } else if (formStatus === "Rejected" || formStatus === "Approved") {
        aggregatedData.Completed += formCount;
      }

      // Aggregate ratings for "Approved" submissions
      if (formStatus === "Approved" && formSubmissionData?.length > 0) {
        formSubmissionData.forEach((submission) => {
          if (submission?.performanceReviewSubmission) {
            const rating = submission.performanceReviewSubmission.rating;
            if (rating != null) {
              // Only consider valid ratings
              totalRating += rating;
              ratingCount++;
            }
          }
        });
      }
    });

    // Calculate average rating
    const averageRating = ratingCount > 0 ? totalRating / ratingCount : 0;

    // You can use `averageRating` in your component or return it from this function
    setTeamReview(averageRating);

    return defaultData;
  };

  const options = {
    plugins: {
      legend: {
        display: false, // Disable the built-in legend
      },
    },
  };

  const colors = processedData?.datasets[0]?.backgroundColor;
  const counts = processedData?.datasets[0]?.data;
  const countBackgroundColors = [
    "#ff316933",
    "#00a0cf33",
    "#9f49a333",
    "#00b4b033",
    "#ffa52333",
  ];
  const shadowColors = [
    "#fad3e7b3",
    "#00a0cf33",
    "#9f49a333",
    "#00b4b033",
    "#ffa52347",
  ];

  // Line Chart Data
  const lineChartData = {
    labels: ["2020", "2021", "2022", "2023"], // X-axis labels
    datasets: [
      {
        label: "Your Rating",
        data: [0, 4.0, 4.0, 4.2], // Dataset 1 (Your Rating)
        borderColor: "#003f66",
        backgroundColor: "rgba(0, 63, 102, 0.2)",
        pointBackgroundColor: "#003f66",
        tension: 0.4,
      },
      {
        label: "Your Team Rating",
        data: [0, 3.6, 3.5, 3.6], // Dataset 2 (Team Rating)
        borderColor: "#00a0cf",
        backgroundColor: "rgba(0, 160, 207, 0.2)",
        pointBackgroundColor: "#00a0cf",
        tension: 0.4,
      },
    ],
  };
  const lineChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
        position: "right",
        labels: {
          usePointStyle: true,
          boxWidth: 10,
        },
      },
    },
    scales: {
      x: {
        ticks: {
          font: {
            size: 10, // Reduce font size for X-axis labels
          },
          maxRotation: 0, // Prevent label rotation
          minRotation: 0,
        },
        grid: {
          display: false,
        },
      },
      y: {
        grid: {
          drawBorder: false,
        },
        ticks: {
          stepSize: 0.5,
        },
      },
    },
  };

  return (
    <div
      style={{
        width: "100%",
        margin: "0 auto",
        textAlign: "center",
        display: "flex",
        flexDirection: "column",
        columnGap: "20px",
      }}
    >
      <div>
        <h2 style={{ fontSize: "18px", fontWeight: "700" }}>
          Team Stats - Review 2024
        </h2>
        <div style={{ display: "flex" }}>
          <div
            style={{
              position: "relative",
              height: "200px",
              width: "180px",
              margin: "0 auto",
            }}
          >
            <Doughnut
              data={processedData}
              options={options}
              style={{ marginTop: "24px" }}
            />
          </div>
          <div
            style={{
              marginTop: "34px",
              textAlign: "left",
              display: "inline-block",
            }}
          >
            {processedData?.labels.map((label, index) => (
              <div key={index}>
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    margin: "8px 0",
                  }}
                >
                  <div
                    style={{
                      width: "13px",
                      height: "13px",
                      backgroundColor: colors[index],
                      marginRight: "14px",
                      borderRadius: "15px",
                      boxShadow: `0 0px 1px 4px ${shadowColors[index]}`,
                    }}
                  ></div>
                  <span
                    style={{
                      marginRight: "10px",
                      width: "64%",
                      fontSize: "14px",
                    }}
                  >
                    {label}{" "}
                  </span>
                  <span
                    style={{
                      padding: "2px 8px",
                      width: "36px",
                      textAlign: "center",
                      borderRadius: "8px",
                      color: "#004890",
                      fontSize: "14px",
                      fontWeight: "600",
                      backgroundColor: countBackgroundColors[index],
                    }}
                  >
                    {counts[index]}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      <div>
        <div
          style={{
            width: "100%",
            margin: "0 auto",
            marginTop: "6%",
            textAlign: "center",
          }}
        >
          <h2
            style={{
              fontSize: "18px",
              fontWeight: "bold",
              marginBottom: "20px",
            }}
          >
            Your Rating per Year
          </h2>
          <div
            style={{
              display: "flex",
              flexDirection: "row",
              columnGap: "16px",
              height: "180px",
            }}
          >
            <div style={{ width: "52%" }}>
              <Line data={lineChartData} options={lineChartOptions} />
            </div>
            {/* Dataset Labels and Values Below Line Chart */}
            <div style={{ textAlign: "left", display: "inline-block" }}>
              {lineChartData.datasets.map((dataset, index) => (
                <div
                  key={index}
                  style={{
                    display: "flex",
                    alignItems: "center",
                    margin: "8px 0",
                  }}
                >
                  <div
                    style={{
                      width: "15px",
                      height: "12px",
                      backgroundColor: dataset.borderColor,
                      marginRight: "14px",
                      borderRadius: "15px",
                      boxShadow: `0 0px 1px 4px ${dataset.backgroundColor}`,
                    }}
                  ></div>
                  <span style={{ width: "100%", fontSize: "14px" }}>
                    {dataset.label}{" "}
                  </span>
                </div>
              ))}
            </div>
          </div>
          <div
            style={{ display: "flex", justifyContent: "end", marginTop: "8px" }}
          >
            <a
              href="https://demo.papyrrus.com/form-builder-studio/PerformanceReview"
              target="_blank"
              rel="noopener noreferrer"
            >
              <Button
                className="secondary-button-color"
                style={{ width: "200px", height: "45px", fontSize: "14px" }}
                label="Open Last Review"
              />
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};
