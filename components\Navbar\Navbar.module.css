/* .navbarContainer {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  background-color: #004890;
  padding: 0 200px;
  height: 100px;
  position: relative;
  z-index: 1000;
} */

@media (max-width: 768px) {
  .navbarContainer {
    flex-direction: column;
    height: auto;
    padding: 0px;
  }
}

.navbarContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;

  @media (max-width: 768px) {
    padding: 4px 8px;
  }
}

.headerLogo {
  width: 4%;
  height: 4%;

  @media (max-width: 768px) {
    width: 11%;
    height: 10%;
  }
}

.menuItems {
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  width: 100%;
}

.menuToggle {
  display: none;
  font-size: 24px;
  background: none;
  border: none;
  cursor: pointer;
}

.notificationIcon {
  cursor: pointer;
}

@media (max-width: 768px) {
  .menuItems {
    display: none;
    flex-direction: column;
    position: absolute;
    top: 60px;
    right: 10px;
    background-color: #004890;
    padding: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border-radius: 5px;
  }

  .menuItems.show {
    display: flex;
  }

  .menuToggle {
    display: block;
  }
}

.profileWrap {
  width: 68%;
  text-align: right;
  margin-left: 10px;
  margin-right: 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;

  @media (max-width: 768px) {
    width: auto;
    text-align: left;
    margin-left: 0px;
    margin-right: 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
}

.rightsecWrap {
  display: flex;
  width: 40%;

  @media (max-width: 768px) {
    flex-direction: row-reverse;
  }
}


.profileContainer {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  align-content: center;
  gap: 8px;
  width: 64%;

  @media (max-width: 768px) {
    width: 14%;
  }
}

.nameContainer {
  font-size: 16px;
  font-weight: bold;
  color: #fff;
  padding: 0px;
}

.roleContainer {
  font-size: 14px;
  color: #fff;
  font-weight: 500;
}

.notificationHeader {
  color: #004890;
  font-size: 20px;
  font-weight: 700;
  padding-bottom: 2%;
}