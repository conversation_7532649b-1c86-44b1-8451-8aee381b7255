import React from "react";
import styles from "../../../WidgetDashboard/WidgetDashboard.module.css";
import Image from "next/image";
import HelpDeskImage from "../../../../public/Logos/HelpDesk.jpg";

export const HelpDesk = ({}) => {
  return (
    <>
      <Image src={HelpDeskImage} alt="Help Desk" width={320} height={210} />
      <ul className={styles.helpDeskContent}>
        <li>
          <p>
            <strong>Help Desk - </strong>
            {`Call us at 702-651-4357 (HELP) (24x7). Email us at Help.Desk@${process.env.NEXT_PUBLIC_PROPERTY_NAME}.edu (24x7).`}
            <a
              href="https://synoptek.service-now.com/sau-portal"
              target="_blank"
              rel="noopener noreferrer"
            >
              {" "}
              Access Self-service Portal
            </a>
          </p>
        </li>
        <li>
          <p>
            <strong>{`${process.env.NEXT_PUBLIC_PROPERTY_NAME} Call Center - `}</strong>
            {`Call 702-651-4357. General questions such as assistance with
            navigating My${process.env.NEXT_PUBLIC_PROPERTY_NAME} and questions related to registration, payment,
            counseling, etc. Available M-F 9am-7pm.`}
          </p>
        </li>
        <li>
          <p>
            <strong>Cybersecurity Awareness - </strong>
            <a
              href="https://www.sau.edu/ots-cybersecurity"
              target="_blank"
              rel="noopener noreferrer"
            >
              Visit oursite
            </a>
            .
          </p>
        </li>
        <li>
          <p>
            <strong>Wireless Connect Instructions - </strong>
            <a
              href="https://www.sau.edu/_saumedia/documents/documents/sauwireless_connect_instructions.pdf"
              target="_blank"
              rel="noopener noreferrer"
            >
              Visit oursite
            </a>
            .
          </p>
        </li>
      </ul>
    </>
  );
};
