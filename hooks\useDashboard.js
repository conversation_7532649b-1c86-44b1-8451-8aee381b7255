import { useState } from "react"

export const useDashboard = ({ initialLazyParams, defaultSortField }) => {
    const [selection, setSelection] = useState(undefined)
    const [rows, setRows] = useState(undefined)
    const [selectedRow, setSelectedRow] = useState()
    const [totalCount, setTotalCount] = useState(0) 
    const [lazyParams, setLazyParams] = useState(
        initialLazyParams || {
            first: 0,
            page: 0,
            rows: 10,
            sortField: defaultSortField || 'lastUpdatedAtUtc',
            sortOrder: -1,
            filters: {
                'global': { value: '', matchMode: 'contains' }
            }
        }
    )

    const onSort = (event) => {
        setLazyParams(event)
    }

    const onPage = (event) => {
        setLazyParams(event)
    }

    const onFilter = (event) => {
        event['first'] = 0
        setLazyParams(event)
    }

    const onSelection = (event) => {
        setSelection(event.value)
    }

    const lazyParamsToQueryString = (lazyParams) => {
        let queryString = '?'
        for (const key in lazyParams) {
            if (key !== 'filters') {
                if (lazyParams[key] !== null && lazyParams[key] !== undefined) {
                    if (key === 'first') continue
                    queryString += `${encodeURIComponent(key)}=${encodeURIComponent(lazyParams[key])}&`
                }
            } else if (lazyParams?.filters?.global?.value) {
                queryString += `global=${encodeURIComponent(lazyParams.filters.global.value)}&`
            }
        }
        return queryString.slice(0, -1); // Remove the trailing '&'
    }

    const [globalFilter, setGlobalFilter] = useState('')
    const [timer, setTimer] = useState(undefined)
    const onGlobalFilterChange = (e) => {
        const value = e.target.value
        if (globalFilter === value) {
          return;
        }
        setGlobalFilter(value)
        clearTimeout(timer)
    
        const newTimer = setTimeout(() => {
            setLazyParams(prevLazyParams => ({
                ...prevLazyParams,
                filters: {
                    ...prevLazyParams.filters,
                    global: { value, matchMode: 'contains' },
                },
            }))
        }, 1000)
    
        setTimer(newTimer)
    }

    return { rows, setRows, totalCount, setTotalCount, selectedRow, lazyParams, globalFilter, lazyParamsToQueryString, onSort, onFilter, onPage, onGlobalFilterChange, selection, onSelection }
}
