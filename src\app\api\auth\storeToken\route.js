import { cookies } from "next/headers"
import { encryptToken } from "../../../../../utilities/encrypt";

export async function POST(req) {
  const body = await req.json()
  const accessToken = body?.accessToken
  console.log("testtttt", accessToken)
  if (!accessToken) {
    return Response.json({ error: "Access token is required" });
  }

  // Set the token in an HttpOnly cookie

  const cookieStore = cookies()

  const encryptAccessToken = encryptToken(accessToken)

  cookieStore.get("Set-Cookie", "accessToken", encryptAccessToken, {
    httpOnly: true,
    secure: true,
    sameSite: "Strict",
    path: "/",
    localStorage: true
  });

  return Response.json({ message: "Token stored successfully" })

}