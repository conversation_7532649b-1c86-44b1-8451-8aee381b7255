import {
  Inter,
  <PERSON>o,
  Open_Sans,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Source_Sans_3,
  Ubuntu,
  Merri<PERSON>ather,
} from "next/font/google";
import { Providers } from "../../components/Providers";
import { PrimeReactProvider } from "primereact/api";

import "primeicons/primeicons.css";
import "./globals.css";
import "primereact/resources/themes/lara-light-cyan/theme.css";
import Head from "next/head";

export const metadata = {
  icons: { icon: "SauTab-icon.svg" },
  title: "EdPortal - Papyrus ®",
  description: "EdPortal - Papyrus ®",
};

export default function RootLayout({ children }) {
  return (
    <Providers>
      <PrimeReactProvider>
        <html lang="en">
          <body>{children}</body>
        </html>
      </PrimeReactProvider>
    </Providers>
  );
}
