import { Open_Sans } from "next/font/google";
import { Providers } from "../../components/Providers";
import { PrimeReactProvider } from "primereact/api";

import "primeicons/primeicons.css";
import "./globals.css";
import "primereact/resources/themes/lara-light-cyan/theme.css";

// Configure Open Sans font with optimizations
const openSans = Open_Sans({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-open-sans",
  weight: ["400", "600", "700"],
});

export const metadata = {
  icons: { icon: "SauTab-icon.svg" },
  title: "EdPortal - Papyrus ®",
  description: "EdPortal - Papyrus ®",
};

export default function RootLayout({ children }) {
  return (
    <Providers>
      <PrimeReactProvider>
        <html lang="en">
          <body className={openSans.className}>{children}</body>
        </html>
      </PrimeReactProvider>
    </Providers>
  );
}
