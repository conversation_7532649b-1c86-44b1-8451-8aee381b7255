import CSNLogo from "../../public/SSOImages/CSNLogo.png";
import CSNHeader<PERSON>ogo from "../../public/SSOImages/logo_whiteNew.svg";
import SauLogo from "../../public/SSOImages/SauLogo.svg";
import SauHeader<PERSON>ogo from "../../public/SSOImages/SSOTransparentWhite.webp";
import SauLogoNew from "../../public/SSOImages/logo_whiteNew.svg";
import SauHeaderLogoNew from "../../public/SSOImages/logo_whiteNew.svg";
// import DoCreativ from "../../public/SSOImages/Docreative.svg";
import PapyrrusLogo from "../../public/Logos/Papyrrus-Logo.svg";
import PapyrusFlower from "../../public/NewIcons/Papyrus_logo.svg";
import MySau from "../../public/SSOImages/My_SAU.png";
import MyCsn from "../../public/NewIcons/myCSN.png";
import SauTab from "../../public/SSOImages/My_SAU.png";
import CsnTab from "../../public/SSOImages/favicon.svg";
import TexasLogo from "../../public/SSOImages/Texas.png";

const logoMapping = {
  CSN: CSNLogo,
  SAU: SauLogo,
  SAUNEW: SauLogoNew,
  UTS: TexasLogo,
};

const headerLogoMapping = {
  CSN: CSNHeaderLogo,
  SAU: SauHeaderLogo,
  SAUNEW: SauHeaderLogoNew,
  UTS: TexasLogo,
};

const papyrusLogoMapping = {
  CSN: PapyrusFlower,
  SAU: PapyrrusLogo,
};

const oracleLogoMapping = {
  CSN: MyCsn,
  SAU: MySau,
  UTS: TexasLogo,
};

const tabLogoMapping = {
  CSN: SauTab,
  SAU: CsnTab,
};

export const getDynamicLogo = () => {
  const propertyName = process.env.NEXT_PUBLIC_PROPERTY_NAME || "CSN";
  const Logo = logoMapping[propertyName];

  if (!Logo) {
    console.warn(
      `Logo for property "${propertyName}" not found. Using default logo.`
    );
  }

  return Logo || logoMapping["CSN"];
};

export const getDynamicHeaderLogo = () => {
  const propertyName = process.env.NEXT_PUBLIC_PROPERTY_NAME || "CSN";
  const Logo = headerLogoMapping[propertyName];

  if (!Logo) {
    console.warn(
      `Logo for property "${propertyName}" not found. Using default logo.`
    );
  }

  return Logo || headerLogoMapping["CSN"];
};

export const getDynamicPapyrusLogo = () => {
  const propertyName = process.env.NEXT_PUBLIC_PROPERTY_NAME || "CSN";
  const Logo = papyrusLogoMapping[propertyName];

  if (!Logo) {
    console.warn(
      `Logo for property "${propertyName}" not found. Using default logo.`
    );
  }

  return Logo || papyrusLogoMapping["CSN"];
};

export const getDynamicOracleLogo = () => {
  const propertyName = process.env.NEXT_PUBLIC_PROPERTY_NAME || "CSN";
  const Logo = oracleLogoMapping[propertyName];

  if (!Logo) {
    console.warn(
      `Logo for property "${propertyName}" not found. Using default logo.`
    );
  }

  return Logo || oracleLogoMapping["CSN"];
};

export const getDynamicTabLogo = () => {
  const propertyName = process.env.NEXT_PUBLIC_PROPERTY_NAME || "CSN";
  const Logo = tabLogoMapping[propertyName];

  if (!Logo) {
    console.warn(
      `Logo for property "${propertyName}" not found. Using default logo.`
    );
  }

  return Logo || tabLogoMapping["CSN"];
};
