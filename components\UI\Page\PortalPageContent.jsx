"use client";
import { useContext, useEffect, useRef, useState } from "react";
import {
  AuthenticatedTemplate,
  useMsalAuthentication,
  UnauthenticatedTemplate,
} from "@azure/msal-react";
import { Navbar } from "../../../components/Navbar/Navbar";
import {
  Tab,
  TabList,
  MenuContainer,
  InputSearch,
  OverlayButton,
} from "../../../components/Menu/Menu";
import { Calendar } from "primereact/calendar";
import Image from "next/image";
//Images
import CanvasLogo from "../../../public/Logos/Canvas-Logo.png";
import CanvasLogo2 from "../../../public/Logos/Canvas-Logo2.png";
// import DoCreativ from "../../../public/SSOImages/Docreative.svg";
import PapyrusFlower from "../../../public/Logos/PapyrusFlower.png";
import CampusSolutions from "../../../public/Logos/CampusSolutions.png";
import PapyrrusLogo from "../../../public/Logos/Papyrrus-Logo.svg";
import PurchaseIcon from "../../../public/Logos/PurchaseIcon.png";
import ServiceNowLogo from "../../../public/Logos/ServiceNow-Logo.png";
import GreyMatterLogo from "../../../public/Logos/GreyMatter-Logo.png";
import AnthologyReachLogo from "../../../public/Logos/AnthologyReach-Logo.png";
import BlackboardLogo from "../../../public/Logos/Blackboard-Logo.png";
import EllucianLogo from "../../../public/Logos/EllucianBanner.png";
import WorkdayLogo from "../../../public/Logos/Workday-Logo.png";
import PerformanceReviewLogo from "../../../public/Logos/New/Performance.png";
import ProjectRequisitionLogo from "../../../public/Logos/New/ProjectReq.png";
import EmployeeOnboardingLogo from "../../../public/Logos/New/EmployeeOnboarding.png";
import OraclePeoplesoft from "../../../public/Logos/OraclePeoplesoft.png";
import PurchaseRequisitionLogo from "../../../public/Logos/New/Purchase.png";
import HelpDeskLogo from "../../../public/Logos/New/HelpDesk.png";
import JobRequisitionLogo from "../../../public/Logos/New/JobRequisition.png";
import Policies from "../../../public/Logos/Policies & Procedures Library.svg";
import ITAccessRequestLogo from "../../../public/Logos/New/ITAccess.png";
import SupplierQualificationsLogo from "../../../public/Logos/New/Suplier.svg";
import EmployeeStatusChangeLogo from "../../../public/Logos/New/ContractManagement.png";
import AppOpen from "../../../public/SSOImages/AppOpen.svg";
import BackgroundImage from "../../../public/SSOImages/BackgroundImage.png";
import styles from "../../../src/app/page.module.css";
import { ConditionalDisplay } from "../../../components/ConditionalDisplay/ConditionalDisplay";
import { WidgetDashboard } from "../../../components/WidgetDashboard/WidgetDashboard";
import useLocalStorage from "../../../hooks/useLocalStorage";
import LoginPage from "../../../components/LoginPage/LoginPage";
import { BreadCrumb } from "primereact/breadcrumb";
import { UserProfileContext } from "../../../contexts/UserProfileContext";
import SauLogo from "../../../public/SSOImages/SSOTransparentWhite.png";
import { PortalTile } from "../../../components/UI/Page/PortalTile";
import { PortalContainer } from "../../../components/UI/Page/PortalContainer";
import { AiChart } from "../Widgets/AiChat/AiChat";
import Footer from "../Footer/Footer";
// import DoCreativFlower from "../../../public/SSOImages/Docreativ_flower.svg";

export const PortalPageContent = ({
  dbData,
  peoplesoftData,
  appointmentData,
  transferHireData,
  workdayData,
  absenseData,
  holidayCalenderData,
}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const today = new Date().toISOString().split("T")[0];
  const appointmentPanelRef = useRef(null);
  const [favorites, setFavorites] = useLocalStorage("favorites", []);
  const [selectedDate, setSelectedDate] = useState(today);
  const userProfile = useContext(UserProfileContext);
  const { firstName } = userProfile;

  const portalTabKey = "widget";
  const [activeTab, setActiveTab] = useState(portalTabKey);
  const [isBlur, setIsBlur] = useState(false);

  useEffect(() => {}, [
    dbData,
    peoplesoftData,
    appointmentData,
    transferHireData,
    workdayData,
    absenseData,
    holidayCalenderData,
  ]);

  const notificationsMockData = [
    {
      content: {
        header: "New Submission Available",
        dateTime: "30-Nov-2023 01:29:25 PM PST",
        details: "View",
      },
      image: CanvasLogo2,
      imageWidth: 40,
      imageHeight: 40,
    },
    {
      content: {
        header: "New Vendor Form Available",
        dateTime: "29-Nov-2023 01:29:25 PM PST",
        details: "View",
      },
      image: PurchaseIcon,
      imageWidth: 40,
      imageHeight: 40,
    },
    {
      content: {
        header: "Expense Report Due",
        dateTime: "28-Nov-2023 01:29:25 PM PST",
        details: "View",
      },
      image: OraclePeoplesoft,
      imageWidth: 40,
      imageHeight: 40,
    },
    {
      content: {
        header: "New Form Requires Approval",
        dateTime: "27-Nov-2023 01:29:25 PM PST",
        details: "View",
      },
      image: PapyrusFlower,
      imageWidth: 35,
      imageHeight: 35,
    },
  ];

  const tiles = [
    // {
    //   image: PapyrrusLogo,
    //   label: "Papyrus",
    //   imageWidth: 200,
    //   imageHeight: 75,
    //   link: "https://demo.papyrrus.com/form-builder-studio",
    // },
    {
      image: PapyrrusLogo,
      label: "Papyrus",
      imageWidth: 200,
      imageHeight: 75,
      link: "https://demo.papyrrus.com/form-builder-studio",
    },
    {
      image: CanvasLogo,
      label: "Canvas",
      imageWidth: 250,
      imageHeight: 75,
    },
    {
      image: CampusSolutions,
      label: "PeopleSoft",
    },
    {
      image: WorkdayLogo,
      label: "Workday",
      imageWidth: 200,
      imageHeight: 75,
    },
    {
      image: GreyMatterLogo,
      label: "greymatter",
      imageWidth: 225,
      imageHeight: 80,
      link: "https://greymatter-preprod.crm.dynamics.com/main.aspx?appid=3949b5de-c2d3-eb11-bacc-0022481ea6d4&forceUCI=1&pagetype=webresource&webresourceName=foundry_papyruspage",
    },
    {
      image: AnthologyReachLogo,
      label: "Anthology Reach",
      imageWidth: 200,
      imageHeight: 70,
    },
    {
      image: EllucianLogo,
      label: "Ellucian",
      imageWidth: 290,
      imageHeight: 110,
    },
    {
      image: BlackboardLogo,
      label: "Blackboard",
      imageWidth: 200,
      imageHeight: 60,
    },
    {
      image: ServiceNowLogo,
      label: "ServiceNow",
      imageWidth: 200,
      imageHeight: 40,
    },
    {
      image: PerformanceReviewLogo,
      label: "Performance Review",
      tileName: "Performance Review",
      imageWidth: 70,
      imageHeight: 94,
      link: "https://demo.papyrrus.com/form-builder-studio/PerformanceReview",
    },
    {
      image: ProjectRequisitionLogo,
      label: "Project Requisition",
      tileName: "Project Requisition",
      imageWidth: 70,
      imageHeight: 94,
      link: "https://demo.papyrrus.com/form-builder-studio/ProjectRequisition",
    },
    {
      image: EmployeeOnboardingLogo,
      label: "Employee Onboarding",
      tileName: "Employee Onboarding",
      imageWidth: 70,
      imageHeight: 94,
      link: "https://demo.papyrrus.com/form-builder-studio/OnBoarding",
    },
    {
      image: PurchaseRequisitionLogo,
      label: "Purchase Requisition",
      tileName: "Purchase Requisition",
      imageWidth: 70,
      imageHeight: 94,
      link: "https://demo.papyrrus.com/form-builder-studio/PurchaseOrder",
    },
    {
      image: HelpDeskLogo,
      label: "Help Desk",
      tileName: "Help Desk",
      imageWidth: 70,
      imageHeight: 94,
      link: "https://demo.papyrrus.com/form-builder-studio/MyTickets",
    },
    {
      image: JobRequisitionLogo,
      label: "Job Requisition",
      tileName: "Job Requisition",
      imageWidth: 70,
      imageHeight: 94,
      link: "https://demo.papyrrus.com/form-builder-studio/JobRequestion",
    },
    {
      image: Policies,
      label: "Policies and Procedures",
      tileName: "Policies and Procedures",
      imageWidth: 70,
      imageHeight: 94,
      link: "https://demo.papyrrus.com/form-builder-studio/Poilcy-and-procedures",
    },
    {
      image: ITAccessRequestLogo,
      label: "Access Request",
      tileName: "Access Request",
      imageWidth: 70,
      imageHeight: 94,
      link: "https://demo.papyrrus.com/form-builder-studio/AccessRequest",
    },
    {
      image: SupplierQualificationsLogo,
      label: "Supplier Qualifications",
      tileName: "Supplier Qualifications",
      imageWidth: 60,
      imageHeight: 94,
      link: "https://demo.papyrrus.com/form-builder-studio/Supplier",
      marginBottom: "16px",
    },
    {
      image: EmployeeStatusChangeLogo,
      label: "Employee Status Change",
      tileName: "Employee Status Change",
      imageWidth: 60,
      imageHeight: 94,
    },
  ];

  const filteredTiles = tiles.filter((tile) =>
    tile.label.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredFavorites = favorites.filter((favoriteLabel) =>
    favoriteLabel.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <main className={styles.mainWrapper}>
      <AuthenticatedTemplate>
        <Navbar
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          notificationsMockData={notificationsMockData}
        />
        <div className={styles.contentWrapper}>
          <div className={styles.overlayContent}>
            <AiChart />
            <div
              className={styles.searchCalendar}
              condition={activeTab === "edPortalsApp"}
            ></div>
            <div className={styles.searchContainer}>
              <InputSearch value={searchQuery} onChange={setSearchQuery} />
            </div>

            <ConditionalDisplay condition={activeTab === "edPortalsApp"}>
              <PortalContainer isBlur={isBlur}>
                {filteredTiles.map((tile, index) => (
                  <PortalTile
                    key={index}
                    image={tile.image}
                    label={tile.label}
                    imageWidth={tile.imageWidth}
                    imageHeight={tile.imageHeight}
                    link={tile.link}
                    favorites={favorites}
                    setFavorites={setFavorites}
                    starIcon={true}
                    tileName={tile.tileName}
                  />
                ))}
              </PortalContainer>
            </ConditionalDisplay>
            <ConditionalDisplay condition={activeTab === portalTabKey}>
              <WidgetDashboard
                isBlur={isBlur}
                dbData={dbData}
                peoplesoftData={peoplesoftData}
                appointmentData={appointmentData}
                transferHireData={transferHireData}
                workdayData={workdayData}
                absenseData={absenseData}
                holidayCalenderData={holidayCalenderData}
              />
            </ConditionalDisplay>
            <ConditionalDisplay condition={activeTab === "myEdPortals"}>
              <PortalContainer isBlur={isBlur}>
                {filteredFavorites.length === 0 ? (
                  <PortalTile
                    key={"PapyrusKey"}
                    image={PapyrrusLogo}
                    label={"Papyrus"}
                    imageWidth={200}
                    imageHeight={75}
                    link={"https://demo.papyrrus.com/form-builder-studio"}
                  />
                ) : (
                  filteredFavorites.map((favoriteLabel) => {
                    const tile = tiles.find((t) => t.label === favoriteLabel);
                    return (
                      tile && (
                        <PortalTile
                          key={tile.label}
                          image={tile.image}
                          label={tile.label}
                          imageWidth={tile.imageWidth}
                          imageHeight={tile.imageHeight}
                          link={tile.link}
                          favorites={favorites}
                          setFavorites={setFavorites}
                          tileName={tile.tileName}
                        />
                      )
                    );
                  })
                )}
              </PortalContainer>
            </ConditionalDisplay>
          </div>
        </div>
        <Footer />
      </AuthenticatedTemplate>
      <UnauthenticatedTemplate>
        <LoginPage />
      </UnauthenticatedTemplate>
    </main>
  );
};
