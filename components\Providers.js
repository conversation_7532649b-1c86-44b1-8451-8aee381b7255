"use client";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@azure/msal-react";
import { UserProfileContextProvider } from "../contexts/UserProfileContext";
import { UserImageContextProvider } from "../contexts/UserImageContext";
import { EventType, PublicClientApplication } from "@azure/msal-browser";
import {
  formBuilderApiRequest,
  msalConfig,
} from "../utilities/Msal/msalConfig";

export const msalInstance = new PublicClientApplication(msalConfig);

let popupWindow = null;

msalInstance.initialize().then(() => {
  // Account selection logic is app dependent. Adjust as needed for  different use cases.
  const accounts = msalInstance.getAllAccounts();
  if (accounts.length > 0) {
    msalInstance.setActiveAccount(accounts[0]);
  }

  msalInstance.addEventCallback(async (event) => {
    if (event.eventType === EventType.LOGIN_SUCCESS && event.payload.account) {
      msalInstance.setActiveAccount(event.payload.account);
      await fetch("/api/auth/storeToken", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ accessToken: accessTokenResponse.accessToken }),
      });
    } else if (
      event.eventType === EventType.SSO_SILENT_SUCCESS &&
      event.payload.account
    ) {
      msalInstance.setActiveAccount(event.payload.account);
    } else if (event.eventType === EventType.POPUP_OPENED) {
      popupWindow = event.payload.popupWindow;
    } else if (event.eventType === EventType.ACQUIRE_TOKEN_FAILURE) {
      console.log("Error while accuiring the token");
    } else if (event.eventType === EventType.ACQUIRE_TOKEN_SUCCESS) {
      console.log("Token acquire successful", msalInstance.getActiveAccount());
      const accessTokenResponse = await msalInstance.acquireTokenSilent({
        ...formBuilderApiRequest,
        account: msalInstance.getActiveAccount(),
      });
      // console.log("Access Token:", accessTokenResponse.accessToken);

      // Send access token to server (if needed)
      await fetch("/api/auth/storeToken", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ accessToken: accessTokenResponse.accessToken }),
      });
    } else if (event.eventType === EventType.LOGOUT_SUCCESS) {
      msalInstance.setActiveAccount(null);
      console.log("Logout success");
    }
  });
});

export const handleSignInClick = () => {
  if (popupWindow && !popupWindow.closed && popupWindow.focus) {
    popupWindow.focus();
  } else {
    msalInstance.loginPopup();
  }
};

export function Providers({ children }) {
  return (
    <MsalProvider instance={msalInstance}>
      <UserProfileContextProvider>
        <UserImageContextProvider>{children}</UserImageContextProvider>
      </UserProfileContextProvider>
    </MsalProvider>
  );
}
