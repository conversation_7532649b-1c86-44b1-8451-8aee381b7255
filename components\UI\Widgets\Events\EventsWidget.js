import React, { useEffect, useState } from "react";
import { Calendar } from "primereact/calendar";
import styles from "./Events.module.css";
import { format } from "date-fns";
import {
  formatDateForHeader,
  formatTime,
} from "../../../../utilities/DateTimeConverter/DateTimeConverter";

export const HolidayCalendarWidget = ({ holidayCalenderData }) => {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [specialDates, setSpecialDates] = useState({
    holiday: [],
    convocation: [],
    academic: [],
  });

  // Format date for comparison
  const formatDateForComparison = (date) =>
    format(new Date(date), "yyyy-MM-dd");

  // Group dates by type
  const groupDatesByType = (data) => {
    const grouped = { holiday: [], convocation: [], academic: [] };

    data?.forEach((event) => {
      const formattedDate = event.date;
      grouped[event.type].push(formattedDate);
    });

    setSpecialDates(grouped);
  };

  useEffect(() => {
    groupDatesByType(holidayCalenderData);
  }, [holidayCalenderData]);

  // Render events for the selected date
  const renderEventsForSelectedDate = () => {
    const formattedSelectedDate = formatDateForComparison(selectedDate);

    const eventsOnSelectedDate = holidayCalenderData?.filter(
      (event) => event.date === formattedSelectedDate
    );

    if (eventsOnSelectedDate?.length === 0) {
      return <div>No events on this date.</div>;
    }

    return eventsOnSelectedDate?.map((event, index) => (
      <div key={index} className={styles.eventContainer}>
        <div className={styles.eventDetails}>
          <div><strong>{event.name} - </strong>
            <strong>{event.type}</strong></div>
        </div>
      </div>
    ));
  };

  // Customize date rendering
  const dateTemplate = (date) => {
    const formattedDate = `${date.year}-${String(date.month + 1).padStart(
      2,
      "0"
    )}-${String(date.day).padStart(2, "0")}`;
    const isHoliday = specialDates.holiday.includes(formattedDate);
    const isConvocation = specialDates.convocation.includes(formattedDate);
    const isAcademic = specialDates.academic.includes(formattedDate);
  
    const isToday = formattedDate === formatDateForComparison(new Date());
    const isSelectedDate = formattedDate === formatDateForComparison(selectedDate);
  
    return (
      <div
        style={{
          position: "relative",
          width: "100%",
          height: "100%",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          // Only apply the border if the date is special and not the selected or current date
          ...(isHoliday || isConvocation || isAcademic
            ? isToday || isSelectedDate
              ? {}
              : { border: "1px solid #ced5e1", borderRadius: "10px" }
            : {}),
        }}
      >
        {date.day}
      </div>
    );
  };

  return (
    <div style={{ display: "flex", flexDirection: "column" }}>
      <Calendar
        style={{
          width: "100%",
          justifyContent: "center",
          alignContent: "center",
        }}
        inline
        value={selectedDate}
        onChange={(e) => setSelectedDate(e.value)}
        dateTemplate={dateTemplate}
      />
      <div className={styles.eventContainer}>
        {renderEventsForSelectedDate()}
      </div>
    </div>
  );
};

export const EventsWidget = ({ holidayCalenderData, group }) => {
  const [selectedDate, setSelectedDate] = useState(
    new Date(new Date().setHours(0, 0, 0, 0))
  );

  function groupEventsByDate(events) {
    const groups = {};
    events.forEach((event) => {
      const dateKey = formatDateForHeader(event.eventDate);
      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      groups[dateKey].push(event);
    });
    return groups;
  }

  function isEventInRange(eventDate, rangeStart, rangeEnd) {
    const event = new Date(eventDate);
    return event >= rangeStart && event <= rangeEnd;
  }

  function renderEvents() {
    const rangeEnd = new Date(selectedDate);
    rangeEnd.setDate(rangeEnd.getDate() + 30);

    const filteredEvents = holidayCalenderData?.filter((event) => {
      const eventDate = new Date(event.date);
      return isEventInRange(eventDate, selectedDate, rangeEnd);
    });
    const groupedEvents = groupEventsByDate(filteredEvents);

    if (Object.keys(groupedEvents).length === 0) {
      return <div>No events found for the selected period.</div>;
    }

    return Object.keys(groupedEvents).map((dateKey, index) => (
      <div key={index}>
        <div className={styles.dateHeader}>{dateKey}</div>
        {groupedEvents[dateKey].map((event, eventIndex) => (
          <div key={eventIndex} className={styles.appointmentContainer}>
            <div className={styles.leftContainer}>
              {/* <div className={styles.eventTime}>{formatTime(event.eventStartTime)} - {formatTime(event.eventEndTime)}</div> */}
              <div>
                <strong>Event Name:</strong> {event.type}
              </div>
              <div>
                <strong>Description:</strong> {event.name}
              </div>
            </div>
          </div>
        ))}
      </div>
    ));
  }

  const handleClick = () => {
    window.open(
      "https://www.csn.edu/search?f.Tabs%7C1a3f58ca-a034-4614-97e6-56b30d710b01%7Eds-csn-events-redesign=Events&profile=csn-he-redesign&query=%21nullquery&collection=1a3f58ca-a034-4614-97e6-56b30d710b01%7Esp-csn-higher-education-redesign"
    );
  };

  return (
    <div>
      <Calendar
        style={{ width: "100%", height: "400px" }}
        inline
        showIcon
        value={selectedDate}
        onChange={(e) =>
          setSelectedDate(new Date(e.value.setHours(0, 0, 0, 0)))
        }
      />
      <div className={styles.eventContainer}>{renderEvents()}</div>
      <div className={styles.viewLink} onClick={handleClick}>
        View All
      </div>
    </div>
  );
};
