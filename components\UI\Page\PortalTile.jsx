"use client";
import { useState } from "react";
import Image from "next/image";
import AppOpen from "../../../public/SSOImages/AppOpen.svg";
import styles from "../../../src/app/page.module.css";

export const PortalTile = ({
  image,
  label,
  imageWidth,
  imageHeight,
  link,
  favorites,
  setFavorites,
  starIcon = false,
  tileName,
}) => {
  const isInitiallyStarFilled = favorites?.includes(label);
  const [isStarFilled, setIsStarFilled] = useState(isInitiallyStarFilled);

  const handleClick = () => {
    if (link) {
      window.open(link);
    }
  };

  const handleStarClick = (e) => {
    e.stopPropagation();
    const newIsStarFilled = !isStarFilled;
    setIsStarFilled(newIsStarFilled);

    if (Array.isArray(favorites)) {
      const newFavorites = newIsStarFilled
        ? [...favorites, label]
        : favorites.filter((favoriteLabel) => favoriteLabel !== label);
      setFavorites(newFavorites);
    }
  };

  return (
    <div className={styles.tileContainer} onClick={handleClick}>
      <div className={styles.appOpenImage}>
        <Image
          src={AppOpen.src}
          alt="Open App"
          className={styles.openAppIcon}
          width={75}
          height={75}
        />
      </div>
      <div className={styles.tileHoverOverlay}>
        <div className={styles.contentToBlur}>
          {starIcon && (
            <div className={styles.iconAlignment} onClick={handleStarClick}>
              <i
                className={`${
                  isStarFilled ? "pi pi-bookmark-fill" : "pi pi-bookmark"
                } ${isStarFilled ? styles.starIconFilled : styles.starIcon}`}
              ></i>
            </div>
          )}
          <div className={styles.portalImage}>
            <Image
              src={image}
              alt="portal Image"
              width={imageWidth}
              height={imageHeight}
            />
          </div>
        </div>
        <div className={styles.tileName}>{tileName}</div>
      </div>
      <div className={styles.labelOutside}>{label}</div>
    </div>
  );
};
