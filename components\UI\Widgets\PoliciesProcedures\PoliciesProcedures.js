import React, { useState, useEffect } from "react";
import styles from "../../../WidgetDashboard/WidgetDashboard.module.css";
import ReactDOM from "react-dom";
import { Doughnut } from "react-chartjs-2";
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from "chart.js";
import { But<PERSON> } from "primereact/button";
import { Line } from "react-chartjs-2";
import {
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
} from "chart.js";
import { Dropdown } from "primereact/dropdown";
import { formBuilderApiRequest } from "../../../../utilities/Msal/msalConfig";
import { useAccount, useMsal, useMsalAuthentication } from "@azure/msal-react";
import { useDashboard } from "../../../../hooks/useDashboard";
import { SubmissionStages } from "../../../../utilities/Msal/constants";
import { useApi } from "../../../../hooks/useApi";
import useUtilityFunctions from "../../../../hooks/useUtilityFunctions";
import { InteractionType } from "@azure/msal-browser";
import { MultiSelect } from "primereact/multiselect";
import emptyImage from "../../../../public/NewIcons/Emptystate_chart.svg";
import doughnutImg from "../../../../public/NewIcons/doughnut_emptyimg.svg";
import Image from "next/image";

const api = process.env.NEXT_PUBLIC_FORM_BUILDER_API_DEMO;

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

ChartJS.register(ArcElement, Tooltip, Legend);

export const PoliciesProcedures = () => {
  const { accounts } = useMsal();
  const { loading, callApi } = useApi();
  const account = useAccount(accounts[0] ?? {});
  const { createDashboardDate } = useUtilityFunctions();
  const { acquireToken } = useMsalAuthentication(
    InteractionType.Silent,
    formBuilderApiRequest
  );
  const initialLazyParams = {
    first: 0,
    page: 0,
    rows: 10,
    sortField: "initiatedDate",
    sortOrder: -1,
    filters: {
      global: {
        value: "",
        matchMode: "contains",
      },
    },
  };

  const {
    rows,
    setRows,
    totalCount,
    setTotalCount,
    lazyParams,
    globalFilter,
    lazyParamsToQueryString,
    onSort,
    onPage,
    onFilter,
    onGlobalFilterChange,
  } = useDashboard({ initialLazyParams });

  // Options for the dropdowns
  const activeDepartment = [
    { label: "Active Policies by Department", value: "policies" },
    { label: "Active Certified Procedures by Department", value: "procedures" },
  ];

  const expirationDropdown = [
    {
      label: "Policy Expiration Schedule",
      value: "policies",
      title: "Policy Expiration Schedule",
    },
    {
      label: "Certified Procedures Expiration Schedule",
      value: "procedures",
      title: "Certified Procedures Expiration Schedule",
    },
  ];

  const [selectedDepartments, setSelectedDepartments] = useState([]); // State for MultiSelect
  const [selectedDepartmentOption, setDropdownOptions2] = useState([]); // Dropdown options
  const [pieChartData, setPieChartData] = useState(null); // State for Pie Chart Data
  const [filteredData, setFilteredData] = useState(null);
  const [filteredData1, setFilteredData1] = useState(null);
  const [expirationData, setSecondDoughnutData] = useState(null);
  const [selectedActiveType, setActiveDepartment] = useState("policies");
  // const [selectedExpiration, setSelectedExpiration] = useState(null);
  const [selectedExpiration, setSelectedExpiration] = useState(
    expirationDropdown[0]?.value
  );
  const [selectedExpDepartments, setExpirationDepartment] = useState([]);
  const [combinedData, setCombinedData] = useState([]);
  const [allDepartmentCount, setDepartmentZero] = useState(true);
  const [multiSelectHistory, setMultiSelectHistory] = useState({});

  useEffect(() => {
    const loadLazyData = async () => {
      if (account) {
        const { accessToken } = await acquireToken();
        const queryString = lazyParamsToQueryString(lazyParams);
        const baseUrl = "/GetAllDepartment";
        const userFormApprovalUrl = `${api}${baseUrl}`;

        const formSubmissionParams = {
          method: "GET",
          url: userFormApprovalUrl,
          headers: {
            Accept: "*/*",
            Authorization: `Bearer ${accessToken}`,
            "next-action": "userFormApproval",
          },
          data: {
            query: queryString,
          },
        };

        const result = await callApi(formSubmissionParams);
        const mutedColors = [
          "#d99bff",
          "#ff9a62",
          "#85b6ff",
          "#ffd233",
          "#4ecb71",
          "#D3D3D3",
          "#B0C4DE",
          "#AFEEEE",
          "#FFE4C4",
          "#D8BFD8",
          "#BDB76B",
          "#C0C0C0",
          "#CD853F",
          "#87CEEB",
          "#98FB98",
          "#FFB6C1",
          "#E6E6FA",
          "#F0E68C",
          "#F5DEB3",
          "#FFDAB9",
          "#E0FFFF",
          "#FAFAD2",
          "#EEE8AA",
          "#ADD8E6",
          "#F08080",
        ];

        const options = result.data?.map((department, index) => ({
          label: department.name,
          value: department.id,
          backgroundColor: mutedColors[index % mutedColors.length],
        }));
        setDropdownOptions2(options);
      }
    };

    loadLazyData();
  }, [lazyParams, acquireToken, account]);

  // Fetch Pie Chart data
  useEffect(() => {
    const fetchPieChartData = async () => {
      if (account) {
        const { accessToken } = await acquireToken();
        const pieChartUrl = `${api}/PoliciesAndProcedures/PieChart`;

        const pieChartParams = {
          method: "GET",
          url: pieChartUrl,
          headers: {
            Accept: "*/*",
            Authorization: `Bearer ${accessToken}`,
          },
        };
        const result = await callApi(pieChartParams);
        if (result?.data) {
          setPieChartData(result.data);
        }
      }
    };

    fetchPieChartData();
  }, [acquireToken, account]);

  // Fetch Second Doughnut Chart Data
  useEffect(() => {
    const fetchSecondDoughnutData = async () => {
      if (account) {
        const { accessToken } = await acquireToken();
        const secondDoughnutUrl = `${api}/PoliciesAndProcedures`;

        const secondDoughnutParams = {
          method: "GET",
          url: secondDoughnutUrl,
          headers: {
            Accept: "*/*",
            Authorization: `Bearer ${accessToken}`,
          },
        };
        const result = await callApi(secondDoughnutParams);
        if (result?.data) {
          setSecondDoughnutData(result.data);
        }
      }
    };

    fetchSecondDoughnutData();
  }, [acquireToken, account]);

  // Recalculate MultiSelect options and filtered data
  useEffect(() => {
    if (pieChartData && selectedDepartmentOption.length > 0) {
      const { totalResult, allCountsZero } = combineData(
        selectedDepartmentOption,
        pieChartData
      );

      setFilteredData(totalResult);
      console.log(allCountsZero);
      setDepartmentZero(allCountsZero);
      // setSelectedDepartments([]); // Reset MultiSelect when Dropdown changes
    }
  }, [pieChartData, selectedDepartmentOption, selectedActiveType]);

  function combineData(selectedDepartmentOption, pieChartData) {
    const documentTypeFilter = selectedActiveType === "procedures" ? 2 : 1;
    const pieChartMap = new Map(
      pieChartData
        ?.filter((data) => data.documentType === documentTypeFilter)
        ?.map((data) => [data.departmentId, data.count])
    );

    let allCountsZero = true; // Flag to track if all counts are zero

    const totalResult = selectedDepartmentOption?.map((department) => {
      const count = pieChartMap.get(department.value) || 0;

      // Update the flag if any count is non-zero
      if (count !== 0) {
        allCountsZero = false;
      }

      return {
        ...department,
        count,
      };
    });

    return { totalResult, allCountsZero };
  }

  // Dropdown change handler

  const handleDropdownChange = (e) => {
    const selectedValue = e.value;

    setMultiSelectHistory((prevHistory) => ({
      ...prevHistory,
      [selectedActiveType]: selectedDepartments,
    }));

    setActiveDepartment(selectedValue);

    if (multiSelectHistory[selectedValue]) {
      setSelectedDepartments(multiSelectHistory[selectedValue]);
    } else {
      setSelectedDepartments(
        selectedDepartmentOption.map((option) => option.value)
      );
    }
  };

  const handleDropdownChange1 = (e) => {
    const selectedValue = e.value;

    // Update the selected expiration state
    setSelectedExpiration(selectedValue);

    // Clear the selected departments in the MultiSelect
    setExpirationDepartment([]);

    // Recalculate data for the MultiSelect dropdown
    if (expirationData && selectedDepartmentOption.length > 0) {
      const recalculatedData = recalculateMultiSelectData(
        selectedDepartmentOption,
        expirationData,
        selectedValue
      );
      setFilteredData1(recalculatedData); // Update filtered data
    }
  };

  const recalculateMultiSelectData = (
    departmentOptions,
    expirationData,
    selectedType
  ) => {
    const documentTypeFilter = selectedType === "policies" ? 1 : 2;

    const expirationMap = new Map(
      expirationData
        ?.filter((data) => data.documentType === documentTypeFilter)
        ?.map((data) => [data.departmentId, data.count])
    );

    // Combine department data with recalculated values
    return departmentOptions.map((department) => {
      const count = expirationMap.get(department.value) || 0;
      return { ...department, count };
    });
  };

  useEffect(() => {
    if (expirationData && selectedDepartmentOption.length > 0) {
      const recalculatedData = recalculateMultiSelectData(
        selectedDepartmentOption,
        expirationData,
        selectedExpiration
      );
      setFilteredData1(recalculatedData);
    }
  }, [selectedExpiration, expirationData, selectedDepartmentOption]);

  useEffect(() => {
    const updatedData = selectedDepartmentOption.map((department) => {
      let documentTypeFilter = 1;
      if (selectedExpiration === "policies") {
        documentTypeFilter = 1;
      } else if (selectedExpiration === "procedures") {
        documentTypeFilter = 2;
      }
      const expiration = expirationData?.find(
        (exp) =>
          exp.departmentId === department.value &&
          exp.documentType === documentTypeFilter
      );
      return {
        ...department,
        expirationData: expiration || null,
      };
    });

    setCombinedData(updatedData);
  }, [
    selectedExpiration,
    selectedActiveType,
    expirationData,
    selectedDepartmentOption,
  ]);

  const getChartData = (selectedExpDepartments) => {
    const selectedData = combinedData?.filter((dep) =>
      selectedExpDepartments.includes(dep.value)
    );

    if (!selectedData.length) return { labels: [], counts: [] };

    const aggregatedData = selectedData.reduce(
      (acc, department) => {
        const { expirationData } = department || {};
        if (expirationData) {
          acc.overdue += expirationData.overDue || 0;
          acc.dueInLessThanOneMonth +=
            expirationData.dueInLessThanOneMonth || 0;
          acc.dueInOneToThreeMonths +=
            expirationData.dueInOneToThreeMonths || 0;
          acc.dueInThreeToSixMonths +=
            expirationData.dueInThreeToSixMonths || 0;
        }
        return acc;
      },
      {
        overdue: 0,
        dueInLessThanOneMonth: 0,
        dueInOneToThreeMonths: 0,
        dueInThreeToSixMonths: 0,
      }
    );

    return {
      labels: ["Overdue", "Due < 1 Month", "Due 1-3 Month", "Due 3-6 Month"],
      countValue: [
        aggregatedData.overdue,
        aggregatedData.dueInLessThanOneMonth,
        aggregatedData.dueInOneToThreeMonths,
        aggregatedData.dueInThreeToSixMonths,
      ],
    };
  };
  useEffect(() => {
    if (selectedDepartmentOption?.length > 0) {
      setSelectedDepartments(selectedDepartmentOption.map((opt) => opt.value));
    }
    if (filteredData1?.length > 0) {
      setExpirationDepartment(filteredData1.map((opt) => opt.value));
    }
  }, [selectedDepartmentOption, filteredData1]);

  // Example usage with multiple selected departments
  const { labels, countValue } = getChartData(selectedExpDepartments);

  // Update the doughnut chart data
  const dataTwo = {
    labels: labels,
    datasets: [
      {
        data: countValue,
        backgroundColor: ["#74c9ff", "#d4d14b", "#f6c76f", "#6dce72"],
      },
    ],
  };

  const colorsTwo = dataTwo.datasets[0].backgroundColor;
  const countsTwo = dataTwo.datasets[0].data;

  // First Doughnut chart data
  const data = {
    labels: [
      "Academic Services",
      "Accreditation Strategic Planning",
      "Finance and  Administration",
      "Foundation",
      "Government Affairs",
    ],
    datasets: [
      {
        data: [10, 5, 6, 8, 3],
        backgroundColor: [
          "#d99bff",
          "#ff9a62",
          "#85b6ff",
          "#ffd233",
          "#4ecb71",
        ],
        borderColor: ["#fff", "#fff", "#fff", "#fff", "#fff"],
        borderWidth: 1,
      },
    ],
  };

  const options = {
    plugins: {
      legend: {
        display: false, // Disable the built-in legend
      },
    },
  };

  const colors = data.datasets[0].backgroundColor;
  const counts = data.datasets[0].data;
  const countBackgroundColors = [
    "#d99bff33",
    "#ff9a6233",
    "#85b6ff33",
    "#ffd23333",
    "#4ecb7133",
    "#D3D3D333",
    "#B0C4DE33",
    "#AFEEEE33",
    "#FFE4C433",
    "#D8BFD833",
    "#BDB76B33",
    "#C0C0C033",
    "#CD853F33",
    "#87CEEB33",
    "#98FB9833",
    "#FFB6C133",
    "#E6E6FA33",
    "#F0E68C33",
    "#F5DEB333",
    "#FFDAB933",
    "#E0FFFF33",
    "#FAFAD233",
    "#EEE8AA33",
    "#ADD8E633",
    "#F0808033",
    "#d99bff33",
    "#ff9a6233",
    "#85b6ff33",
    "#ffd23333",
    "#4ecb7133",
    "#D3D3D333",
    "#B0C4DE33",
    "#AFEEEE33",
    "#FFE4C433",
    "#D8BFD833",
    "#BDB76B33",
    "#C0C0C033",
    "#CD853F33",
    "#87CEEB33",
    "#98FB9833",
    "#FFB6C133",
    "#E6E6FA33",
    "#F0E68C33",
    "#F5DEB333",
    "#FFDAB933",
    "#E0FFFF33",
    "#FAFAD233",
    "#EEE8AA33",
    "#ADD8E633",
    "#F0808033",
  ];
  const shadowColors = [
    "#d99bff66",
    "#ff9a6266",
    "#85b6ff80",
    "#ffd23366",
    "#4ecb7166",
    "#D3D3D366",
    "#B0C4DE66",
    "#AFEEEE66",
    "#FFE4C466",
    "#D8BFD866",
    "#BDB76B66",
    "#C0C0C066",
    "#CD853F66",
    "#87CEEB66",
    "#98FB9866",
    "#FFB6C166",
    "#E6E6FA66",
    "#F0E68C66",
    "#F5DEB366",
    "#FFDAB966",
    "#E0FFFF66",
    "#FAFAD266",
    "#EEE8AA66",
    "#ADD8E666",
    "#F0808066",
    "#d99bff66",
    "#ff9a6266",
    "#85b6ff80",
    "#ffd23366",
    "#4ecb7166",
    "#D3D3D366",
    "#B0C4DE66",
    "#AFEEEE66",
    "#FFE4C466",
    "#D8BFD866",
    "#BDB76B66",
    "#C0C0C066",
    "#CD853F66",
    "#87CEEB66",
    "#98FB9866",
    "#FFB6C166",
    "#E6E6FA66",
    "#F0E68C66",
    "#F5DEB366",
    "#FFDAB966",
    "#E0FFFF66",
    "#FAFAD266",
    "#EEE8AA66",
    "#ADD8E666",
    "#F0808066",
  ];

  // Data for the second Doughnut chart
  const dataTwos = {
    labels: ["Overdue", "Due < 1 Month", "Due 1-3 Month", "Due 3-6 Month"],
    datasets: [
      {
        data: [7, 4, 9, 12],
        backgroundColor: ["#74c9ff33", "#d4d14b33", "#f6c76f33", "#6dce7233"],
        borderColor: ["#85b6ff80", "#d4d14b80", "#f6c76f80", "#6dce7280"],
        borderWidth: 1,
      },
    ],
  };

  const optionsTwo = {
    plugins: {
      legend: {
        display: false, // Disable the built-in legend
      },
    },
  };

  // Colors and other settings for the second chart
  const colorsTwos = dataTwos.datasets[0].backgroundColor;
  const countsTwos = dataTwos.datasets[0].data;
  const countBackgroundColorsTwo = [
    "#74c9ff33",
    "#d4d14b33",
    "#f6c76f33",
    "#6dce7233",
  ];
  const shadowColorsTwo = ["#85b6ff80", "#d4d14b80", "#f6c76f80", "#6dce7280"];
  const itemTemplate = (option) => (
    <div
      title={option.label}
      className="w-40 overflow-hidden whitespace-nowrap text-ellipsis"
    >
      {option.label}
    </div>
  );

  return (
    <div>
      <div
        style={{
          width: "100%",
          margin: "0 auto",
          textAlign: "center",
          display: "flex",
          flexDirection: "column",
          columnGap: "20px",
        }}
      >
        <div>
          {/* Dropdown Row */}
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              gap: "20px",
              margin: "8px 0 0 0",
            }}
          >
            {/* First Dropdown */}
            <Dropdown
              className="customMultiselect"
              value={selectedActiveType}
              options={activeDepartment}
              onChange={handleDropdownChange}
              placeholder="select"
              style={{
                width: "46%",
                border: "0.5px solid #004890",
                borderRadius: "0px",
                padding: "6px 10px",
                fontSize: "15px",
                fontFamily: "Open Sans",
              }}
              itemTemplate={itemTemplate}
            />
            {/* MultiSelect for Departments */}
            <MultiSelect
              className="customMultiselect"
              value={selectedDepartments}
              options={selectedDepartmentOption}
              onChange={(e) => setSelectedDepartments(e.value)}
              placeholder="Select Departments"
              display="chip"
              style={{
                width: "46%",
                border: "0.5px solid #004890",
                borderRadius: "0px",
                padding: "6px 10px",
                fontSize: "15px",
                fontFamily: "Open Sans",
              }}
              showSelectAll // Enables the "Select All" feature
              selectAllLabel="Select All" // Customizes the label for the "Select All" option
              itemTemplate={itemTemplate}
            />
          </div>

          <div style={{ display: "flex", columnGap: "22px" }}>
            <div
              style={{
                position: "relative",
                height: "200px",
                width: "100%",
                margin: "0px auto",
              }}
            >
              {selectedDepartments.length === 0 ||
              !filteredData ||
              filteredData.length === 0 ? (
                <Image
                  src={emptyImage}
                  alt="Empty State"
                  width={100}
                  height={100}
                  style={{
                    objectFit: "contain",
                    position: "absolute",
                    top: "50%",
                    left: "50%",
                    transform: "translate(-50%, -50%)",
                  }}
                />
              ) : (
                <div
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    columnGap: "20px",
                  }}
                >
                  <div style={{ width: "45%" }}>
                    {!allDepartmentCount ? (
                      <span
                        style={{
                          width: "180px",
                          height: "180px",
                          display: "block",
                        }}
                      >
                        <Doughnut
                          data={{
                            labels: selectedDepartments.map((id) => {
                              const dept = filteredData.find(
                                (dept) => dept.value === id
                              );
                              return dept ? dept.label : ""; // Return department label
                            }),
                            datasets: [
                              {
                                data: selectedDepartments.map((id) => {
                                  const department = filteredData.find(
                                    (dept) => dept.value === id
                                  );
                                  return department ? department.count : 0; // Return count for department
                                }),
                                backgroundColor: selectedDepartments.map(
                                  (id) => {
                                    const department = filteredData.find(
                                      (dept) => dept.value === id
                                    );
                                    return (
                                      department?.backgroundColor || "#000000"
                                    ); // Use muted color
                                  }
                                ),
                                borderColor: selectedDepartments.map(
                                  () => "#fff"
                                ),
                                borderWidth: 1,
                              },
                            ],
                          }}
                          options={options}
                          style={{ marginTop: "24px" }}
                        />
                      </span>
                    ) : (
                      <Image
                        src={doughnutImg}
                        alt="New Empty State"
                        width={100}
                        height={100}
                        style={{
                          objectFit: "contain",
                          position: "absolute",
                          top: "50%",
                          left: "22%",
                          transform: "translate(-50%, -50%)",
                        }}
                      />
                    )}
                  </div>

                  <div
                    style={{
                      marginTop: "20px",
                      textAlign: "left",
                      display: "inline-block",
                      width: "50%",
                      maxHeight: "182px",
                      overflowY: "scroll",
                      scrollbarWidth: "thin",
                    }}
                  >
                    {selectedDepartments
                      .map((id) => {
                        const department = filteredData.find(
                          (dept) => dept.value === id
                        );
                        return department ? { ...department, id } : null;
                      })
                      .filter(Boolean)
                      .sort((a, b) => b.count - a.count)
                      .map((department) => {
                        const shadowColorIndex = filteredData.findIndex(
                          (dept) => dept.value === department.value
                        );
                        return (
                          <div
                            key={department.id}
                            style={{
                              display: "flex",
                              alignItems: "center",
                              margin: "4px 4px",
                            }}
                          >
                            <div
                              style={{
                                width: "13px",
                                height: "13px",
                                backgroundColor: department?.backgroundColor,
                                marginRight: "20px",
                                borderRadius: "15px",
                                boxShadow: `0 0px 1px 4px ${
                                  shadowColors[shadowColorIndex] || "#000"
                                }`,
                              }}
                            ></div>
                            <span
                              style={{
                                marginRight: "10px",
                                width: "64%",
                                fontSize: "14px",
                              }}
                            >
                              {department.label}
                            </span>
                            <span
                              style={{
                                padding: "2px 8px",
                                width: "36px",
                                textAlign: "center",
                                borderRadius: "8px",
                                color: "#004890",
                                fontSize: "14px",
                                fontWeight: "600",
                                backgroundColor:
                                  countBackgroundColors[shadowColorIndex] ||
                                  "#f0f0f0",
                              }}
                            >
                              {department.count}
                            </span>
                          </div>
                        );
                      })}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Second Doughnut Chart */}
        <div style={{ marginTop: "5px" }}>
          {/* Dropdown Row */}
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              gap: "20px",
              margin: "16px 0 0 0",
            }}
          >
            {/* First Dropdown */}
            <Dropdown
              className="customMultiselect"
              value={selectedExpiration}
              options={expirationDropdown}
              onChange={handleDropdownChange1} // Attach the handler here
              placeholder="select"
              style={{
                width: "46%",
                border: "0.5px solid #004890",
                borderRadius: "0px",
                padding: "6px 10px",
                fontSize: "15px",
                fontFamily: "Open Sans",
              }}
              itemTemplate={itemTemplate}
            />
            {/* Multiselect Department Dropdown */}
            <MultiSelect
              className="customMultiselect"
              value={selectedExpDepartments}
              options={filteredData1}
              onChange={(e) => setExpirationDepartment(e.value)}
              placeholder="Select Departments"
              display="chip"
              style={{
                width: "46%",
                border: "0.5px solid #004890",
                borderRadius: "0px",
                padding: "6px 10px",
                fontSize: "15px",
                fontFamily: "Open Sans",
              }}
              showSelectAll // Enables the "Select All" feature
              selectAllLabel="Select All" // Customizes the label for the "Select All" option
              itemTemplate={itemTemplate}
            />
          </div>
          <div style={{ display: "flex", columnGap: "22px" }}>
            <div
              style={{
                position: "relative",
                height: "188px",
                width: "100%",
                margin: "0 auto",
              }}
            >
              {dataTwo.labels.length === 0 || countsTwo.length === 0 ? (
                <Image
                  src={emptyImage}
                  alt="Empty State"
                  width={100}
                  height={100}
                  style={{
                    objectFit: "contain",
                    position: "absolute",
                    top: "50%",
                    left: "50%",
                    transform: "translate(-50%, -50%)",
                  }}
                />
              ) : (
                <div
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    columnGap: "20px",
                  }}
                >
                  <div style={{ width: "45%" }}>
                    <span
                      style={{
                        width: "180px",
                        height: "180px",
                        display: "block",
                      }}
                    >
                      <Doughnut
                        data={dataTwo}
                        options={optionsTwo}
                        style={{ marginTop: "24px" }}
                      />
                    </span>
                  </div>
                  <div
                    style={{
                      marginTop: "34px",
                      textAlign: "left",
                      display: "inline-block",
                      width: "50%",
                      maxHeight: "182px",
                      overflowY: "scroll",
                      scrollbarWidth: "thin",
                    }}
                  >
                    {dataTwo.labels
                      .map((label, index) => ({
                        label,
                        count: countsTwo[index],
                        color: colorsTwo[index],
                        backgroundColor: countBackgroundColorsTwo[index],
                        shadowColor: shadowColorsTwo[index],
                      }))
                      .sort((a, b) => b.count - a.count) // Sort by count in descending order
                      .map((item, index) => (
                        <div
                          key={index}
                          style={{
                            display: "flex",
                            alignItems: "center",
                            margin: "4px 4px",
                          }}
                        >
                          <div
                            style={{
                              width: "13px",
                              height: "13px",
                              backgroundColor: item.color,
                              marginRight: "20px",
                              borderRadius: "15px",
                              boxShadow: `0 0px 1px 4px ${item.shadowColor}`,
                            }}
                          ></div>
                          <span
                            style={{
                              marginRight: "10px",
                              width: "64%",
                              fontSize: "14px",
                            }}
                          >
                            {item.label}
                          </span>
                          <span
                            style={{
                              padding: "2px 8px",
                              width: "36px",
                              textAlign: "center",
                              borderRadius: "8px",
                              color: "#004890",
                              fontSize: "14px",
                              fontWeight: "600",
                              backgroundColor: item.backgroundColor,
                            }}
                          >
                            {item.count}
                          </span>
                        </div>
                      ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
        <div style={{ display: "flex", justifyContent: "end" }}>
          <a
            href="https://demo.papyrrus.com/form-builder-studio/Poilcy-and-procedures"
            target="_blank"
            rel="noopener noreferrer"
          >
            <Button
              className="secondary-button-color"
              style={{ width: "216px", height: "45px", fontSize: "14px" }}
              label="Open Policies & Procedures"
            />
          </a>
        </div>
      </div>
    </div>
  );
};
