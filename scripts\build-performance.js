#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting build performance analysis...\n');

// Record start time
const startTime = Date.now();

try {
  // Clean previous build
  console.log('🧹 Cleaning previous build...');
  execSync('npm run clean', { stdio: 'inherit' });

  // Run build with timing
  console.log('🔨 Building application...');
  const buildStart = Date.now();
  execSync('npm run build', { stdio: 'inherit' });
  const buildEnd = Date.now();

  const buildTime = (buildEnd - buildStart) / 1000;
  console.log(`\n✅ Build completed in ${buildTime.toFixed(2)} seconds`);

  // Analyze bundle size
  console.log('\n📊 Analyzing bundle size...');
  const nextDir = path.join(process.cwd(), '.next');
  
  if (fs.existsSync(nextDir)) {
    const staticDir = path.join(nextDir, 'static');
    if (fs.existsSync(staticDir)) {
      const bundleSize = getFolderSize(staticDir);
      console.log(`📦 Total bundle size: ${(bundleSize / 1024 / 1024).toFixed(2)} MB`);
    }
  }

  // Generate performance report
  const report = {
    timestamp: new Date().toISOString(),
    buildTime: buildTime,
    bundleSize: fs.existsSync(path.join(nextDir, 'static')) 
      ? getFolderSize(path.join(nextDir, 'static')) 
      : 0,
    nodeVersion: process.version,
    platform: process.platform,
  };

  // Save report
  const reportsDir = path.join(process.cwd(), 'build-reports');
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir);
  }

  const reportFile = path.join(reportsDir, `build-${Date.now()}.json`);
  fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
  
  console.log(`\n📋 Performance report saved to: ${reportFile}`);
  console.log('\n🎉 Build performance analysis complete!');

} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}

function getFolderSize(folderPath) {
  let totalSize = 0;
  
  function calculateSize(dirPath) {
    const files = fs.readdirSync(dirPath);
    
    for (const file of files) {
      const filePath = path.join(dirPath, file);
      const stats = fs.statSync(filePath);
      
      if (stats.isDirectory()) {
        calculateSize(filePath);
      } else {
        totalSize += stats.size;
      }
    }
  }
  
  if (fs.existsSync(folderPath)) {
    calculateSize(folderPath);
  }
  
  return totalSize;
}
