import React, { useState, useMemo, useEffect } from "react";
import { Doughnut } from "react-chartjs-2";
import {
  Chart as ChartJS,
  ArcElement,
  CategoryScale,
  Tooltip,
  Legend,
} from "chart.js";
import styles from "../../../WidgetDashboard/WidgetDashboard.module.css";
import clsx from "clsx";
import { MultiSelect } from "primereact/multiselect";
import { Dropdown } from "primereact/dropdown";
import emptyImg from "../../../../public/NewIcons/Piechart_empty.svg";
import Image from "next/image";

ChartJS.register(ArcElement, CategoryScale, Tooltip, Legend);

export const ClassSchedule = ({ filteredData = [] }) => {
  const [secondDropdownValue, setSecondDropdownValue] = useState([]);

  // Get unique class and section options for the first dropdown
  const classOptions = useMemo(() => {
    const uniqueOptions = new Set(
      filteredData?.map((item) => ({
        label: `${item.class_name} - ${item.section}`,
        value: `${item.class_name} - ${item.section}`,
      }))
    );
    return Array.from(uniqueOptions);
  }, [filteredData]);
  const [firstDropdownValue, setFirstDropdownValue] = useState(
    classOptions[0].value
  );
  // Get title options for the multi-select dropdown based on the selected class
  const titleOptions = useMemo(() => {
    if (!firstDropdownValue) return [];
    const [selectedClass, selectedSection] = firstDropdownValue.split(" - ");
    return filteredData
      .filter(
        (item) =>
          item.class_name === selectedClass && item.section === selectedSection
      )
      .map((item) => ({
        label: item.title,
        value: item.title,
      }));
  }, [firstDropdownValue, filteredData]);
  useEffect(() => {
    if (titleOptions.length > 0) {
      setSecondDropdownValue(titleOptions.map((option) => option.value));
    }
  }, [titleOptions]);

  // Calculate chart data and totals
  const { chartData, totals, dueAt } = useMemo(() => {
    if (!secondDropdownValue.length) {
      return {
        chartData: { labels: [], datasets: [] },
        totals: { pending: 0, submitted: 0 },
        dueAt: "N/A",
      };
    }

    const totals = secondDropdownValue.reduce(
      (acc, selectedTitle) => {
        const selectedRecord = filteredData.find(
          (item) => item.title === selectedTitle
        );
        if (selectedRecord) {
          acc.pending += selectedRecord.pending || 0;
          acc.submitted += selectedRecord.submitted || 0;
        }
        return acc;
      },
      { pending: 0, submitted: 0 }
    );

    let dueAt = "N/A";
    if (secondDropdownValue.length === 1) {
      const selectedRecord = filteredData.find(
        (item) => item.title === secondDropdownValue[0]
      );
      if (selectedRecord?.due_at) {
        dueAt = new Date(selectedRecord.due_at).toLocaleDateString();
      }
    }

    // Prepare chart data for doughnut
    const chartData = {
      labels: ["Pending", "Submitted"],
      datasets: [
        {
          data: [totals.pending, totals.submitted],
          backgroundColor: ["#f97316a8", "#3b82f6a8"],
        },
      ],
    };

    return { chartData, totals, dueAt };
  }, [secondDropdownValue, filteredData]);

  // Handle class change
  const handleClassChange = (e) => {
    setFirstDropdownValue(e.value);
    setSecondDropdownValue([]); // Clear title selection
  };

  return (
    <>
      {/* Dropdown Section */}
      <div
        className={clsx(styles.dropdownSec, "mt-1")}
        style={{ display: "flex", justifyContent: "space-between" }}
      >
        {/* Class Dropdown */}
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            rowGap: "10px",
            width: "45%",
          }}
        >
          <label
            style={{ fontSize: "16px", fontWeight: "700" }}
            htmlFor="dropdown1"
          >
            Class Name:
          </label>
          <Dropdown
            id="dropdown1"
            value={firstDropdownValue}
            options={classOptions}
            onChange={handleClassChange}
            placeholder="Select Class"
            style={{
              width: "100%",
              border: "1px solid #004890",
              borderRadius: "0px",
              padding: "6px 10px",
              fontSize: "15px",
              fontFamily: "Open Sans",
            }}
            itemTemplate={(option) => (
              <div title={option.description || option.label}>
                {option.label}
              </div>
            )}
          />
        </div>

        {/* Title Multi-Select Dropdown */}
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            rowGap: "10px",
            width: "45%",
          }}
        >
          <label
            style={{ fontSize: "16px", fontWeight: "700" }}
            htmlFor="dropdown2"
          >
            Title:
          </label>
          <MultiSelect
            id="dropdown2"
            className="customMultiselect"
            value={secondDropdownValue}
            options={titleOptions}
            onChange={(e) => setSecondDropdownValue(e.value)}
            style={{
              width: "100%",
              border: "1px solid #004890",
              borderRadius: "0px",
              padding: "6px 10px",
              fontSize: "15px",
              fontFamily: "Open Sans",
            }}
            itemTemplate={(option) => (
              <div
                title={option.description || option.label}
                className={styles.textOverflow}
              >
                {option.label}
              </div>
            )}
            placeholder="Select title(s)"
            disabled={!firstDropdownValue}
            showSelectAll // Enables the "Select All" feature
            selectAllLabel="Select All" // Customizes the label for the "Select All" option
          />
        </div>
      </div>

      {/* Doughnut Chart Section */}
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          marginTop: "3rem",
        }}
      >
        {firstDropdownValue.length === 0 || secondDropdownValue.length === 0 ? (
          <Image
            src={emptyImg}
            style={{ marginBottom: "2rem", marginTop: "2rem" }}
          />
        ) : (
          <div style={{ width: "30" }}>
            <Doughnut
              data={chartData}
              options={{
                responsive: true,
                plugins: {
                  legend: {
                    position: "bottom",
                    labels: {
                      usePointStyle: true,
                      pointStyle: "circle",
                      color: "#000",
                      font: {
                        size: 14,
                        family: "Open Sans",
                      },
                      padding: 20,
                    },
                    onClick: null,
                  },
                },
                layout: {
                  margin: {
                    top: 30,
                  },
                },
              }}
              style={{ width: "70%", height: "70%" }}
            />
          </div>
        )}
        <div
          style={{
            marginTop: "2rem",
            textAlign: "center",
            backgroundColor: "#f9fafb",
            padding: "14px 38px",
            width: "100%",
          }}
        >
          <p style={{ margin: 0, fontWeight: "500" }}>
            Pending: <span style={{ fontWeight: "700" }}>{totals.pending}</span>
            , Submitted:{" "}
            <span style={{ fontWeight: "700" }}>{totals.submitted}</span>, Due
            Date: <span style={{ fontWeight: "700" }}>{dueAt}</span>
          </p>
        </div>
      </div>
    </>
  );
};

export const ScheduleWidget = ({
  subject,
  dateTime,
  classCode,
  section,
  location,
  building,
  room,
  displayBuilding = true,
}) => {
  return (
    <div
      style={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        gap: "30px",
      }}
    >
      <div className={styles.subjectContainer}>
        <div className={styles.subjectHeader} style={{ fontWeight: "bold" }}>
          {classCode}
        </div>
        <div style={{ fontWeight: "bold" }}>{subject}</div>
        <div className={styles.dateTime}>{dateTime}</div>
      </div>
      <div className={styles.detailsContainer}>
        <div style={{ display: "flex" }}>
          <div className={styles.boldedDetail}>Campus:</div>
          <div>{location}</div>
        </div>
        {displayBuilding && (
          <>
            <div style={{ display: "flex" }}>
              <div className={styles.boldedDetail}>Building:</div>
              <div>{building}</div>
            </div>
            <div style={{ display: "flex" }}>
              <div className={styles.boldedDetail}>Room:</div>
              <div>{room}</div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};
