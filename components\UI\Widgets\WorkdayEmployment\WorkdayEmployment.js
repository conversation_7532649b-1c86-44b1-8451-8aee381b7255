import React from "react";
import styles from "../../../WidgetDashboard/WidgetDashboard.module.css";

export const WorkdayEmployment = ({
  transferHireData,
  workdayData,
  absenseData,
  dataverseData,
  userSupervisor,
  userName,
}) => {
  const employeeId = userName === "John Keating" ? 30 : 4;
  const effTransferHireDate =
    transferHireData?.length > 0
      ? (() => {
          const record = transferHireData.find(
            (data) => parseInt(data.employee_id) === employeeId
          );
          return record ? new Date(record.eff_transfer_hire_date) : null;
        })()
      : null;

  // Helper function to format dates as MM/DD/YYYY
  const formatDate = (date) => {
    const d = new Date(date);
    const month = String(d.getMonth() + 1).padStart(2, "0"); // Add leading zero to month
    const day = String(d.getDate()).padStart(2, "0"); // Add leading zero to day
    const year = d.getFullYear();
    return `${month}/${day}/${year}`;
  };

  const startEmpDate = effTransferHireDate
    ? formatDate(effTransferHireDate)
    : "N/A";
  const sortedHolidayData = absenseData
    ?.filter((data) => data.employee_id === employeeId) // Filter data for the specified employeeID
    ?.sort((a, b) => {
      return new Date(a.start_date) - new Date(b.start_date); //Sort by start_date
    });

  const holidayElements = sortedHolidayData?.map((holiday, index) => {
    const startDate = formatDate(holiday.start_date);
    const endDate = formatDate(holiday.end_date);

    return (
      <div
        key={index}
        style={{
          display: "flex",
          flexDirection: "row",
          justifyContent: "space-between",
          marginBottom: "2px",
        }}
      >
        <div style={{ fontSize: "15px" }}>{startDate}</div>
        <div style={{ fontSize: "15px" }}>{endDate}</div>
        <div style={{ fontSize: "15px", fontStyle: "italic" }}>
          {holiday.approvername}
        </div>
      </div>
    );
  });

  return (
    <div>
      <div className={styles.workdayContainer}>
        <div className={styles.columnContainer}>
          <div className={styles.workdayHeader}>Supervisor</div>
          <div className={styles.workdayHeaderResult}>
            {userName === "John Keating" ? "Susan Sontag" : userSupervisor}
          </div>{" "}
        </div>
        <div className={styles.columnContainer}>
          <div className={styles.workdayHeader}>Employment Start Date</div>
          <div className={styles.workdayHeaderResult}>{startEmpDate}</div>
        </div>
        <div className={styles.columnContainer}>
          <div className={styles.workdayHeader}>Current Pay Period</div>
          <div className={styles.workdayHeaderResult}>01/16/25 - 01/31/25</div>
        </div>
        <div className={styles.columnContainer}>
          <div className={styles.workdayHeader}>Pay Date</div>
          <div className={styles.workdayHeaderResult}>02/01/25</div>
        </div>
      </div>
      <div className={styles.holidayContainer}>
        <div className="mt-4" style={{ fontWeight: "700", fontSize: "20px" }}>
          Absences Requests
        </div>
        <div>
          {/*Title section (displayed only once) */}
          <div
            style={{
              fontWeight: "600",
              fontSize: "16px",
              marginBottom: "7px",
              display: "flex",
              flexDirection: "row",
              justifyContent: "space-between",
            }}
          >
            <div>Start Date</div>
            <div>End Date</div>
            <div>Approved By</div>
          </div>

          {/* Holiday Entries */}
          {holidayElements}
        </div>
      </div>
    </div>
  );
};
