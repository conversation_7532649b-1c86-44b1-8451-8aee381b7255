import React, { useEffect, useState } from "react";
import { Calendar } from "primereact/calendar"; // PrimeReact Calendar
import { format } from "date-fns"; // date-fns for date formatting
import appoinmentStyles from "./AppointmentsWidget.module.css";
import urlAero from "../../../../public/SSOImages/Url_aero.svg";
import clsx from "clsx";
import Image from "next/image";

export const AppointmentsWidget = ({ appointmentsData, userName }) => {
  const employeeId = userName === "John Keating" ? 30 : 4;
  const appoinmentData =
    appointmentsData?.filter(
      (data) => parseInt(data.employee_id) === employeeId
    ) || null;

  const [selectedDate, setSelectedDate] = useState(new Date());

  // const formatDateForComparison = (date) => format(new Date(date), 'yyyy-MM-dd');


  const formatDateForComparison = (date) => {
    const dt = new Date(date);
    return dt.getFullYear() + "-" + String(dt.getMonth() + 1).padStart(2, '0') + "-" + String(dt.getDate()).padStart(2, '0');
  }

  const [specialDates, setSpecialDates] = useState([]);

  useEffect(() => {
    groupAppointmentsByDate(appoinmentData);
  });

  // Function to customize date rendering
  const dateTemplate = (date) => {
    const formattedDate =
      date.year +
      "-" +
      String(date.month + 1).padStart(2, "0") +
      "-" +
      String(date.day).padStart(2, "0");
    const isSpecial = specialDates.includes(formattedDate);
    const isToday = formattedDate === formatDateForComparison(new Date());
    const isSelected = formatDateForComparison(selectedDate) === formattedDate;

    return (
      <div
        style={{
          position: "relative",
          width: "100%",
          height: "100%",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          ...(isSpecial && !isToday && !isSelected
            ? { border: "1px solid #ced5e1", borderRadius: "10px" }
            : {}),
        }}
      >
        {date.day}
      </div>
    );
  };
  // Group appointments by date
  function groupAppointmentsByDate(appointments) {
    const groups = {};
    appointments?.forEach((appointment) => {
      const dateKey = formatDateForComparison(appointment.start);
      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      groups[dateKey].push(appointment);
    });

    try {
      if (specialDates && specialDates.length == 0) {
        const oppointmentDates = Object.keys(groups);
        setSpecialDates(oppointmentDates);
      }
    } catch (error) {}

    return groups;
  }

  // Filter appointments for the selected date
  function getAppointmentsForSelectedDate() {
    const groupedAppointments = groupAppointmentsByDate(appoinmentData);
    const selectedDateKey = formatDateForComparison(selectedDate);
    return groupedAppointments[selectedDateKey] || [];
  }

  // Format time to 12-hour AM/PM format (example: '4:00 PM')
  const formatTime = (time) => format(new Date(time), 'h:mm a');

  // Render the appointments for the selected date
  function renderAppointments() {
    const appointmentsForSelectedDate = getAppointmentsForSelectedDate();

    if (appointmentsForSelectedDate.length === 0) {
      return <div>No appointments found for the selected date.</div>;
    }

    return appointmentsForSelectedDate.map((appointment, index) => (
      <div
        key={index}
        className={clsx(
          appoinmentStyles.container,
          "appointmentContainer my-3"
        )}
      >
        <div className="leftContainer p-3">
          <div className="eventTime flex justify-between">
            <span>
              {" "}
              {formatTime(appointment.start)} -{" "}
              {appointment.end ? formatTime(appointment.end) : "TBD"}
            </span>

            <span
              className="cursor-pointer"
              onClick={() => window.open(appointment.appointmentlink, "_blank")}
            >
              <Image src={urlAero} alt="url aero" />
            </span>
          </div>
          <div>
            <strong>Student Name:</strong> {appointment.full_legal_name}
          </div>
          <div>
            <strong>Reason:</strong> {appointment.reason}
          </div>
          <div>
            <strong>Student ID:</strong> {appointment.student_id}
          </div>
        </div>
      </div>
    ));
  }

  // Function to determine if a date has any appointments
  function hasAppointmentsOnDate(date) {
    const groupedAppointments = groupAppointmentsByDate(appoinmentData);
    const dateKey = formatDateForComparison(date);
    return (
      groupedAppointments[dateKey] && groupedAppointments[dateKey].length > 0
    );
  }

  // Apply custom class to dates with appointments
  function dateClassName(date) {
    return hasAppointmentsOnDate(date) ? appoinmentStyles.appointmentDay : "";
  }

  return (
    <div style={{ display: "flex", flexDirection: "column" }}>
      <Calendar
        style={{
          width: "100%",
          justifyContent: "center",
          alignContent: "center",
        }}
        inline
        showIcon
        value={selectedDate}
        onChange={(e) => setSelectedDate(e.value)}
        dateClassName={dateClassName} // Add custom class for dates with appointments
        dateTemplate={dateTemplate}
      />
      <div className="eventContainer">{renderAppointments()}</div>
    </div>
  );
};
