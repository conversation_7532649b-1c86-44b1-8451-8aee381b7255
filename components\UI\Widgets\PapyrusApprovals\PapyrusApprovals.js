import React, { useState, useEffect } from "react";
import { Tile, TileContainer } from "../../Tile/Tile";
import { Tab, TabContainer } from "../../Tabs/Tabs";
import { InteractionType } from "@azure/msal-browser";
import { formBuilderApiRequest } from "../../../../utilities/Msal/msalConfig";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
// Icons
import Waiting from "../../../../public/Icons/Waiting.svg";
import Hold from "../../../../public/Icons/Hold.svg";
import Approved from "../../../../public/Icons/Approved.svg";
import Rejected from "../../../../public/Icons/Rejected.svg";
// Hooks
import { useApprovalTileValues } from "../../../../hooks/useApprovalTileValues";
import { useAccount, useMsal, useMsalAuthentication } from "@azure/msal-react";
import { useDashboard } from "../../../../hooks/useDashboard";
import { SubmissionStages } from "../../../../utilities/Msal/constants";
import { useApi } from "../../../../hooks/useApi";
import useUtilityFunctions from "../../../../hooks/useUtilityFunctions";

const tabMapper = {
  InProgress: ["Waiting For Me", "On Hold"],
  Approved: ["Approved"],
  Rejected: ["Rejected"],
};

const iconMapper = {
  "Waiting For Me": Waiting,
  "On Hold": Hold,
  Approved: Approved,
  Rejected: Rejected,
};

const api = process.env.NEXT_PUBLIC_FORM_BUILDER_API_DEMO;

export const PapyrusApprovals = ({ solutionId = 1 }) => {
  const { accounts } = useMsal();
  const { loading, callApi } = useApi();
  const account = useAccount(accounts[0] ?? {});
  const { createDashboardDate } = useUtilityFunctions();
  const { acquireToken } = useMsalAuthentication(
    InteractionType.Silent,
    formBuilderApiRequest
  );
  const headerStyle = { fontWeight: "600", fontSize: "15.5px", color: "#000" };
  const initialLazyParams = {
    first: 0,
    page: 0,
    rows: 10,
    sortField: "initiatedDate",
    sortOrder: -1,
    filters: {
      global: {
        value: "",
        matchMode: "contains",
      },
    },
  };

  const {
    rows,
    setRows,
    totalCount,
    setTotalCount,
    lazyParams,
    globalFilter,
    lazyParamsToQueryString,
    onSort,
    onPage,
    onFilter,
    onGlobalFilterChange,
  } = useDashboard({ initialLazyParams });

  const submittedAtBodyTemplate = (formSubmission) => {
    const { submittedAtUtc } = formSubmission;
    return createDashboardDate(submittedAtUtc);
  };

  const lastUpdatedBodyTemplate = (formSubmission) => {
    const { lastUpdatedAtUtc } = formSubmission;
    return createDashboardDate(lastUpdatedAtUtc);
  };

  const { myApprovalChartValues } = useApprovalTileValues({ solutionId });

  const [selectedTile, setSelectedTile] = useState("InProgress");
  const [selectedTab, setSelectedTab] = useState(tabMapper["InProgress"][0]);

  const handleTileClick = (tile) => {
    setSelectedTile(tile);
    setSelectedTab(tabMapper[tile][0]);
  };

  const handleTabClick = (tab) => {
    setSelectedTab(tab);
  };

  useEffect(() => {
    const loadLazyData = async () => {
      if (account) {
        const { accessToken } = await acquireToken();
        const queryString = lazyParamsToQueryString(lazyParams);
        const urlBaseMapper = {
          "Waiting For Me": `FormSubmission/filter/approver/status/${SubmissionStages.Inprogress}`,
          "On Hold": `FormSubmission/filter/email/status/${SubmissionStages.OnHold}`,
          Approved: `FormSubmission/filter/formTransactions/status/${SubmissionStages.Approved}`,
          Rejected: `FormSubmission/filter/formTransactions/status/${SubmissionStages.Rejected}`,
        };

        const baseUrl = urlBaseMapper[selectedTab];
        if (!baseUrl) {
          return;
        }

        const userFormApprovalUrl = `${api}${baseUrl}${queryString}&solutionId=${solutionId}`;

        const formSubmissionParams = {
          method: "GET",
          url: userFormApprovalUrl,
          headers: {
            Accept: "*/*",
            Authorization: `Bearer ${accessToken}`,
            "next-action": "userFormApproval",
          },
          data: {
            query: queryString,
          },
        };
        const result = await callApi(formSubmissionParams);
        setRows(result?.data?.formSubmissions);
        setTotalCount(result?.data?.count);
      }
    };

    loadLazyData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [lazyParams, acquireToken, account, selectedTab]);

  const handleRowClick = (value) => {
    const baseUrl =
      "https://demo.papyrrus.com/form-builder-studio/view/";
    let url = "";
    url = `${baseUrl}${value.formDefinition.id}/approvals/${value.id}`;
    window.open(url, "_blank");
  };

  return (
    <>
      <TileContainer>
        <Tile
          title="Waiting For Me"
          colorOption="InProgress"
          number={myApprovalChartValues.totalWaitingForMe}
          handleClick={() => handleTileClick("InProgress")}
          isActive={selectedTile === "InProgress"}
        />
        <Tile
          title="Approved"
          colorOption="Approved"
          number={myApprovalChartValues.totalApproved}
          handleClick={() => handleTileClick("Approved")}
          isActive={selectedTile === "Approved"}
        />
        <Tile
          title="Rejected"
          colorOption="Rejected"
          number={myApprovalChartValues.totalRejected}
          handleClick={() => handleTileClick("Rejected")}
          isActive={selectedTile === "Rejected"}
        />
      </TileContainer>
      <TabContainer>
        {tabMapper[selectedTile].map((tab) => (
          <Tab
            key={tab}
            title={tab}
            isActive={selectedTab === tab}
            handleClick={() => handleTabClick(tab)}
            display={true}
            icon={iconMapper[tab]}
          />
        ))}
      </TabContainer>
      <DataTable
        value={rows}
        lazy
        columnResizeMode="expand"
        dataKey="id"
        paginator
        first={lazyParams.first}
        rows={lazyParams.rows}
        onSort={onSort}
        onPage={onPage}
        onFilter={onFilter}
        sortField={lazyParams.sortField}
        sortOrder={lazyParams.sortOrder}
        totalRecords={totalCount}
        filters={lazyParams.filters}
        loading={loading}
        globalFilterFields={[]}
        selectionMode="single"
        onSelectionChange={(e) => handleRowClick(e.value)}
      >
        <Column
          className="dashboardTitle"
          field="formSubmissionId"
          header="Form ID"
          sortable
          headerStyle={{ ...headerStyle, width: "7%" }}
        />
        <Column
          className="dashboardTitle"
          field="formDefinition.name"
          header="Form Title"
          sortable
          headerStyle={{ ...headerStyle, width: "10%" }}
        />
        <Column
          className="dashboardTitle"
          field="submittedAtUtc"
          header="Date Submitted"
          body={submittedAtBodyTemplate}
          sortable
          headerStyle={{ ...headerStyle, width: "11%" }}
        />
        <Column
          className="dashboardTitle"
          field="lastUpdatedAtUtc"
          header="Last Updated"
          body={lastUpdatedBodyTemplate}
          sortable
          headerStyle={{ ...headerStyle, width: "10%" }}
        />
      </DataTable>
    </>
  );
};
