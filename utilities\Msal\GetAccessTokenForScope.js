import { msalInstance } from "../../components/Providers";

export async function getAccessTokenForScope(requestScopes) {
    const account = msalInstance.getActiveAccount()
    if (!account) {
        throw Error("No active account! Verify a user has been signed in and setActiveAccount has been called.");
    }

    const response = await msalInstance.acquireTokenSilent({
        ...requestScopes,
        account: account
    })

    return response.accessToken 
}