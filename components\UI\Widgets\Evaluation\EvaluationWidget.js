import styles from "./EvaluationWidget.module.css";
import Link from 'next/link'
import Image from "next/image";
import star from "../../../../public/SSOImages/starRating.svg"

export const EvaluationWidget = ({ href, link }) => {

  return (
    <div className={styles.evaluationContainer} style={{ backgroundColor: "#fffdf1", padding: "1rem 0px", marginTop: "4px", marginBottom: "2%" }}>
      <div className={styles.holidayContainer} style={{ borderBottom: "1px solid #004990" }}>
        <div style={{ fontWeight: '700', fontSize: "20px", paddingBottom: "10px" }}>My Evaluation for year 2024</div>
      </div>
      <div className={styles.ratingWrap}>
        {/* <span style={{ display: "flex", gap: "10px", justifyContent: "center", margin: "10px 0px" }}>
          <Image src={star} style={{ width: "34px", height: "34px" }} />
          <Image src={star} style={{ width: "34px", height: "34px" }} />
          <Image src={star} style={{ width: "34px", height: "34px" }} />
          <Image src={star} style={{ width: "34px", height: "34px" }} />
          <Image src={star} style={{ width: "34px", height: "34px" }} />
        </span> */}
        <span className={styles.infoHeader}>
          Average Rating:
        </span>
        <div className={styles.ratingNumber}>
          4.2/5
        </div>
      </div>
      <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", width: "84%", color: "#004890" }}>
        <span className={styles.infoHeader}>
          Review Period:
        </span>
        <div>
          07/01/23 - 06/30/24
        </div></div>
      {/* <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", width: "84%", color: "#004890" }}>
        <span className={styles.infoHeader}>
          Current Pay Period:
        </span>
        <div>
          09/01/24 - 09/30/24
        </div></div> */}
      <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", width: "84%", color: "#004890" }}>
        <span className={styles.infoHeader}>
          Pay Date:
        </span>
        <div>
          10/04/24
        </div></div>
      <Link className="mt-2" href={href} style={{ textDecoration: "underline", color: "#3b82f6" }}>
        {link}
      </Link>
    </div>
  )
}