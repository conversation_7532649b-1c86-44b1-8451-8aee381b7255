import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { InputText } from "primereact/inputtext";
import { TabContainer } from "../../Tabs/Tabs";
import { Tab } from "../../Tabs/Tabs";
import { useEffect, useState } from "react";
import { useDashboard } from "../../../../hooks/useDashboard";
import { useApi } from "../../../../hooks/useApi";
import { getAccessTokenForScope } from "../../../../utilities/Msal/GetAccessTokenForScope";
import { formBuilderApiRequest } from "../../../../utilities/Msal/msalConfig";
import { useAccount, useMsal } from "@azure/msal-react";
import { DateColumnTemplate } from "../../DateColumnTemplate/DateColumnTemplate";
import { DatePeriodColumnTemplate } from "../../DatePeriodColumnTemplate/DatePeriodColumnTemplate";
import previousReview from "../../../../public/NewIcons/Previous_Reviews.svg";

const api = process.env.NEXT_PUBLIC_FORM_BUILDER_API;

export const MyPerformance = () => {
  const { accounts } = useMsal();
  const account = useAccount(accounts[0] ?? {});
  const {
    rows,
    setRows,
    totalCount,
    setTotalCount,
    lazyParams,
    globalFilter,
    onGlobalFilterChange,
    onSort,
    onPage,
    onFilter,
  } = useDashboard({});
  const { loading, callApiFetch } = useApi();
  const [currentDashboard, setCurrentDashboard] = useState(0);

  let loadLazyTimeout = null;
  useEffect(() => {
    const loadLazyData = async () => {
      if (loadLazyTimeout) {
        clearTimeout(loadLazyTimeout);
      }

      const accessToken = await getAccessTokenForScope(formBuilderApiRequest);
      const fetchParams = {
        method: "POST",
        headers: {
          Authorization: `Bearer ${accessToken}`,
          Accept: "application/json",
          "Content-Type": "application/json",
          "next-action": "PerformanceReview",
        },
        body: JSON.stringify({
          ...lazyParams,
        }),
      };

      const result = await callApiFetch(
        `${api}PerformanceReview/PreviousReviews`,
        fetchParams
      );
      setRows(result?.rows);
      setTotalCount(result?.totalCount);
    };

    if (account) {
      loadLazyData();
    }
  }, [lazyParams, loadLazyTimeout, account]);

  const handleRowClick = (value) => {
    console.log(value);
    const baseUrl =
      "https://demo.papyrrus.com/form-builder-studio/view/";
    let url = "";
    url = `${baseUrl}${value.formDefinitionId}/mySubmissions/${value.formSubmissionId}`;
    window.open(url, "_blank");
  };

  return (
    <div>
      <TabContainer>
        <Tab
          icon={previousReview}
          title="Previous Reviews"
          display={true}
          isActive={currentDashboard === 0}
          handleClick={() => setCurrentDashboard(0)}
        />
      </TabContainer>
      <DataTable
        value={rows}
        lazy
        loading={loading}
        columnResizeMode="expand"
        dataKey="friendlyFormSubmissionId"
        paginator
        first={lazyParams.first}
        rows={lazyParams.rows}
        totalRecords={totalCount}
        onPage={onPage}
        onSort={onSort}
        onFilter={onFilter}
        sortField={lazyParams.sortField}
        sortOrder={lazyParams.sortOrder}
        filters={lazyParams.filters}
        size="sm"
        selectionMode="single"
        onSelectionChange={(e) => handleRowClick(e.value)}
      >
        <Column
          className="dashboardTitle dashboardHeaderStyle"
          field="friendlyFormSubmissionId"
          header="ID"
          sortable
          headerStyle={{ width: "10%" }}
        />
        <Column
          className="dashboardTitle dashboardHeaderStyle"
          field="formDefinitionMasterName"
          header="Title"
          sortable
          headerStyle={{ width: "10%" }}
        />
        <Column
          className="dashboardTitle dashboardHeaderStyle"
          field="departmentName"
          header="Department"
          sortable
          headerStyle={{ width: "10%" }}
        />
        <Column
          className="dashboardTitle dashboardHeaderStyle"
          field="rating"
          header="Rating"
          sortable
          headerStyle={{ width: "10%" }}
        />
        <Column
          className="dashboardTitle dashboardHeaderStyle"
          field="lastUpdatedAtUtc"
          header="Last Updated"
          sortable
          body={(rowData) => (
            <DateColumnTemplate
              date={rowData?.lastUpdatedAtUtc}
              useTimestampFormat
            />
          )}
          headerStyle={{ width: "10%" }}
        />
        <Column
          className="dashboardTitle dashboardHeaderStyle"
          field="submittedAtUtc"
          header="Submitted On"
          body={(rowData) => (
            <DateColumnTemplate
              date={rowData?.submittedAtUtc}
              useTimestampFormat
            />
          )}
          sortable
          headerStyle={{ width: "10%" }}
        />
      </DataTable>
    </div>
  );
};
