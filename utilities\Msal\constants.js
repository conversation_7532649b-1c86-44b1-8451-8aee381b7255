export const SubmissionStages = {
  Submitted: 1,
  Inprogress: 2,
  Approved: 3,
  Rejected: 4,
  RevisionRequired: 5,
  Completed: 6,
  Processed: 7,
  Cancelled: 8,
  OnHold: 9,
  Recall: 10,
  Draft: 11,
  Resume: 12,
  Restarted: 13,
  Demote: 14
}

export const ReverseSubmissionStages = Object.entries(SubmissionStages)
    .reduce((acc, [key, value]) => {
        acc[value] = key;
        return acc;
    }, {})