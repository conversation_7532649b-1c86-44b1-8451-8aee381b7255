"use client";
import { useContext, useEffect, useRef, useState } from "react";
import { UserProfileContext } from "../../contexts/UserProfileContext";
import { getAccessTokenForScope } from "../../utilities/Msal/GetAccessTokenForScope";
import { dataverseApiRequest } from "../../utilities/Msal/msalConfig";
import { ConditionalDisplay } from "../ConditionalDisplay/ConditionalDisplay";
import styles from "./WidgetDashboard.module.css";
// Logos
import CanvasLogo from "../../public/NewIcons/Canvas_logo.svg";
import OracleLogo from "../../public/Logos/Oracle-Logo.png";
import GreyMatterLogo from "../../public/NewIcons/Greymatter_logo.svg";
import CSNIcon from "../../public/Logos/CSNIcon.png";
import WorkdayLogo from "../../public/NewIcons/Workday_logo.svg";
import PapyrusFlower from "../../public/NewIcons/Papyrus_logo.svg";
import CSNLogo from "../../public/NewIcons/newLogo_backWhite.webp";
import myCSN from "../../public/NewIcons/myCSN.png";
//Widgets
import { ClassSchedule } from "../UI/Widgets/ClassScedule/ClassSchedule";
import { WidgetTile } from "../UI/WidgetTile/WidgetTile";
import { HelpDesk } from "../UI/Widgets/HelpDesk/HelpDesk";
import { Resources } from "../UI/Widgets/Resources/Resources";
import { MyCSN } from "../UI/Widgets/MyCSN/MyCSN";
import { StudentInformation } from "../UI/Widgets/StudentInformation/StudentInformation";
import { ScheduleWidget } from "../UI/Widgets/Schedule/ScheduleWidget";
import { AcademicInformation } from "../UI/Widgets/AcedemicInformation/AcademicInformation";
import { AppointmentsWidget } from "../UI/Widgets/Appointments/AppointmentsWidget";
import { LinksWidget } from "../UI/Widgets/MostUsedLinks/LinksWidget";
import { PerformanceReview } from "../UI/Widgets/PerformanceReview/PerformanceReview";
import { EmployeeOnboarding } from "../UI/Widgets/EmployeeOnboarding/EmployeeOnboarding";
import { PoliciesProcedures } from "../UI/Widgets/PoliciesProcedures/PoliciesProcedures";
import { AiChart } from "../UI/Widgets/AiChat/AiChat";
import { HolidayCalendarWidget } from "../UI/Widgets/Events/EventsWidget";
import { EvaluationWidget } from "../UI/Widgets/Evaluation/EvaluationWidget";
import { PapyrusForms } from "../UI/Widgets/PapyrusForms/PapyrusForms";
import { PapyrusApprovals } from "../UI/Widgets/PapyrusApprovals/PapyrusApprovals";
import { MySignatures } from "../UI/Widgets/MySignatures/MySignatures";
import { RoutingApprovals } from "../UI/Widgets/RoutingApprovals/RoutingApprovals";
import { MyAbsences } from "../UI/Widgets/MyAbsences/MyAbsences";
import { WorkdayEmployment } from "../UI/Widgets/WorkdayEmployment/WorkdayEmployment";
import { HRRequisition } from "../UI/Widgets/HRRequisition/HRRequisition";
import { PurchaseRequisition } from "../UI/Widgets/PurchaseRequisition/PurchaseRequisition";
import { PurchaseOrder } from "../UI/Widgets/PurchaseOrder/PurchaseOrder";
import { Invoice } from "../UI/Widgets/Invoice/Invoice";
import { AssignmentWidget } from "../UI/Widgets/Assignments/AssignmentWidget";
import { MyPerformance } from "../UI/Widgets/MyPerformance/MyPerformance";
import { InputSearch, OverlayButton } from "../../components/Menu/Menu";
import { Calendar } from "primereact/calendar";
import { getDynamicLogo, getDynamicPapyrusLogo, getDynamicOracleLogo } from "../../utilities/LogoMap/logoMapping";

// import { query } from "../api/connection";

export const WidgetDashboard = ({
  isBlur,
  dbData,
  peoplesoftData,
  appointmentData,
  transferHireData,
  workdayData,
  absenseData,
  holidayCalenderData,
}) => {
  const userProfile = useContext(UserProfileContext);
  const userRole = userProfile?.role?.name ?? "";
  const userSupervisor = userProfile?.manager?.displayName ?? "-";
  const defaultDataverseData = {
    professorData: [],
    studentData: [],
    holidayData: [],
    staffData: [],
    appointmentsData: [],
  };
  const defaultFilteredData = {
    professorData: [],
    appointmentsData: [],
    staffData: [],
  };
  const [dataverseData, setDataverseData] = useState(defaultDataverseData);
  const [filteredData, setFilteredData] = useState(defaultFilteredData);
  const [linksData, setLinksData] = useState();

  //const users = await query("SELECT * FROM users", []);

  useEffect(() => {
    const email = userProfile?.email?.toLowerCase() ?? "";

    const fetchData = async () => {
      try {
        const accessToken = await getAccessTokenForScope(dataverseApiRequest);
        const urls = [
          "https://org4e09c964.crm.dynamics.com/api/data/v9.2/cr07c_coursestaughts",
          "https://org4e09c964.crm.dynamics.com/api/data/v9.2/cr07c_collegestudentses",
          "https://org4e09c964.crm.dynamics.com/api/data/v9.2/cr07c_holidayses",
          "https://org4e09c964.crm.dynamics.com/api/data/v9.2/cr07c_ptoandholidayses",
          "https://org4e09c964.crm.dynamics.com/api/data/v9.2/cr07c_staffappointmentses",
        ];

        const fetchParams = {
          method: "GET",
          headers: {
            Authorization: `Bearer ${accessToken}`,
            "OData-MaxVersion": "4.0",
            "OData-Version": "4.0",
            Accept: "application/json",
            "Content-Type": "application/json; charset=utf-8",
            "next-action": "fetchParams",
          },
        };

        const fetchDataFromUrl = async (url) => {
          const response = await fetch(url, fetchParams);
          const data = await response.json();
          return data.value.map((item) => {
            return Object.keys(item)
              .filter((key) => key.startsWith("cr07c_"))
              .reduce((obj, key) => {
                obj[key] = item[key];
                return obj;
              }, {});
          });
        };

        const [
          professorData,
          studentData,
          holidayData,
          staffData,
          appointmentsData,
        ] = await Promise.all(urls.map((url) => fetchDataFromUrl(url)));

        setDataverseData({
          professorData,
          studentData,
          holidayData,
          staffData,
          appointmentsData,
        });

        setFilteredData({
          professorData: professorData.filter(
            (professor) => professor.cr07c_facultyemail.toLowerCase() === email
          ),
          appointmentsData: appointmentsData.filter(
            (appointment) =>
              appointment.cr07c_staffemail.toLowerCase() === email
          ),
          staffData: staffData.filter(
            (staff) => staff.cr07c_employeeemail.toLowerCase() === email
          ),
        });
      } catch (error) {
        console.error("Error fetching data:", error);
        setDataverseData(defaultDataverseData);
        setFilteredData(defaultFilteredData);
      }
    };

    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userProfile]);

  const combineProfessorWithAssignments = (dbData) => {
    if (!dbData || dbData.length === 0) {
      return [];
    }

    return dbData.map((item) => ({
      className: `${item.class_name} - ${item.section}`, // Combine class name and section
      assignment: item.title, // Use the title as the assignment
      dueDate: item.due_at, // Use the due_at as the due date
    }));
    // const { professorData } = filteredData;
    // const assignments = mockAssignments;

    // if (
    //   !professorData ||
    //   professorData.length === 0 ||
    //   !assignments ||
    //   assignments.length === 0
    // ) {
    //   return [];
    // }

    // return professorData.map((professor, index) => {
    //   const mockAssignment = assignments[index % assignments.length];

    //   return {
    //     className: `${professor.cr07c_classcode} `,
    //     assignment: mockAssignment.assignment,
    //     dueDate: mockAssignment.dueDate,
    //   };
    // });
  };

  const convertTo12HourFormat = (time24) => {
    const [hours, minutes] = time24.split(":").map(Number);
    const suffix = hours >= 12 ? "PM" : "AM";
    const hours12 = hours % 12 || 12; // Convert hour to 12-hour format, handling 0 and 12 properly
    const minutesFormatted = minutes < 10 ? `0${minutes}` : minutes;
    return `${hours12}:${minutesFormatted} ${suffix}`;
  };

  const transformSchedule = (records, semType) => {
    return records
      ?.filter((record) => record.semester === semType)
      .map((record) => {
        const scheduleMap = {}; // Store time slots and corresponding days

        // Helper function to check and add schedule for a given day
        const addDaySchedule = (day, startTime, endTime) => {
          if (startTime && endTime) {
            const timeSlot = `${startTime}-${endTime}`;
            if (!scheduleMap[timeSlot]) {
              scheduleMap[timeSlot] = [];
            }
            scheduleMap[timeSlot].push(day);
          }
        };

        // Add the days and times to the schedule map
        addDaySchedule(
          "Mo",
          record.monday_meeting_start_time,
          record.monday_meeting_end_time
        );
        addDaySchedule(
          "Tu",
          record.tuesday_meeting_start_time,
          record.tuesday_meeting_end_time
        );
        addDaySchedule(
          "We",
          record.wednesday_meeting_start_time,
          record.wednesday_meeting_end_time
        );
        addDaySchedule(
          "Th",
          record.thursday_meeting_start_time,
          record.thursday_meeting_end_time
        );
        addDaySchedule(
          "Fr",
          record.friday_meeting_start_time,
          record.friday_meeting_end_time
        );

        // Build the final schedule string by joining days with the same time slot
        const schedule = Object.keys(scheduleMap)
          .map((timeSlot) => {
            const days = scheduleMap[timeSlot].join(""); // Combine days for the same time slot
            const [startTime, endTime] = timeSlot.split("-");
            const start12hr = convertTo12HourFormat(startTime);
            const end12hr = convertTo12HourFormat(endTime);
            return `${days} ${start12hr}-${end12hr}`;
          })
          .join(" ");

        // Return the transformed record
        return {
          className: `${record.name}-${record.section}`,
          schedule: schedule,
          location: `${record.campus} ${record.room}`,
          selected: record.selected, // Keep `selected` for sorting
        };
      })
      .sort((a, b) => {
        // Sort by `selected` ('Y' comes before 'N')
        if (a.selected === "Y" && b.selected === "N") return -1;
        if (a.selected === "N" && b.selected === "Y") return 1;
        return 0; // No change for equal values
      });
  };

  return (
    <div className={isBlur ? `${styles.blurred}` : `${styles.blurBackground}`}>
      {/* <AiChart /> */}
      <div className={styles.portalContainer}>
        <ConditionalDisplay
          condition={userRole === "Contributor" || userRole === "Admin"}
        >
          <WidgetTile
            image={CanvasLogo}
            imageWidth={210}
            imageHeight={180}
            header="Assignments Submitted and Due "
          >
            <ClassSchedule filteredData={dbData} />
          </WidgetTile>
          <WidgetTile
            image={OracleLogo}
            imageWidth={40}
            imageHeight={40}
            titleImage={getDynamicOracleLogo()}
            titleImageWidth={100}
            titleImageHeight={30}
            header={"Class Schedule - Spring 2025"}
          >
            <ScheduleWidget
              data={transformSchedule(peoplesoftData, "Fall 2024")}
            />
          </WidgetTile>
          <WidgetTile
            image={GreyMatterLogo}
            imageWidth={160}
            imageHeight={160}
            header="Student Appointments"
            italics={true}
            actionButton={{
              label: "View in app",
              url: "https://greymatter-preprod.crm.dynamics.com/main.aspx?pagetype=webresource&webresourceName=msdyn_/ScheduleBoard/index.html?data={\"tab\"%3A\"d48c2e19-70d7-ef11-8ee9-6045bdd5d315\"%2C\"startdate\"%3A\"2025-01-20\"%2C\"viewmode\"%3A\"hourly\"%2C\"viewtype\"%3A\"gantt\"%2C\"map\"%3A\"false\"%2C\"columnwidth\"%3A\"50\"}"
            }}
          >
            <AppointmentsWidget appointmentsData={appointmentData}
              userName={userProfile?.displayName} />
          </WidgetTile>
          <ConditionalDisplay
            condition={userProfile?.email != "<EMAIL>"}
          >
            <WidgetTile
              image={CanvasLogo}
              imageWidth={210}
              imageHeight={180}
              header="Assignments Due "
            >
              <AssignmentWidget
                data={combineProfessorWithAssignments(dbData)}
                gradeColor="green"
              />
            </WidgetTile>
          </ConditionalDisplay>
          <WidgetTile
            image={WorkdayLogo}
            imageWidth={150}
            imageHeight={180}
            header={"Workday Employment Info"}
            height={680}
          >
            <WorkdayEmployment
              transferHireData={transferHireData}
              workdayData={workdayData}
              absenseData={absenseData}
              dataverseData={dataverseData}
              userSupervisor={userSupervisor}
              userName={userProfile?.displayName}
            />
            <div>
            </div>
          </WidgetTile>
          <WidgetTile
            className={styles.testingTile}
            imageHeight={210}
            imageWidth={190}
            image={getDynamicLogo()}
            header={"Events, Academic Calendar & Holidays"}
          >
            <HolidayCalendarWidget holidayCalenderData={holidayCalenderData} />
          </WidgetTile>
          <WidgetTile
            image={OracleLogo}
            imageWidth={40}
            imageHeight={40}
            titleImage={getDynamicOracleLogo()}
            titleImageWidth={100}
            titleImageHeight={30}
            header={"Course Load Selections - Summer 2025"}
          >
            <ScheduleWidget
              data={transformSchedule(peoplesoftData, "Summer 2025")}
            />
          </WidgetTile>
          <WidgetTile
            image={getDynamicPapyrusLogo()}
            imageWidth={150}
            imageHeight={180}
            header={"Form Approvals"}
          >
            <PapyrusApprovals />
          </WidgetTile>
          {/* <WidgetTile
            label="Website"
            header={'Most Used Links'}>
            <LinksWidget linksData={linksData} />
          </WidgetTile> */}
          <WidgetTile
            image={getDynamicLogo()}
            imageWidth={210}
            imageHeight={190}
            header={"Faculty & Staff Resources"}
          >
            <Resources />
          </WidgetTile>
          <WidgetTile
            image={getDynamicPapyrusLogo()}
            imageWidth={150}
            imageHeight={180}
            header={"Performance Review"}
            height={680}
          >
            <PerformanceReview userName={userProfile?.displayName} />
          </WidgetTile>
          <WidgetTile
            image={getDynamicPapyrusLogo()}
            imageWidth={150}
            imageHeight={180}
            header={"Employee Onboarding"}
            height={680}
          >
            <EmployeeOnboarding userName={userProfile?.displayName} />
          </WidgetTile>
          <WidgetTile
            image={getDynamicPapyrusLogo()}
            imageWidth={150}
            imageHeight={180}
            header={"Policies and Procedures"}
            height={680}
          >
            <PoliciesProcedures linksData={linksData} />
          </WidgetTile>
          <WidgetTile
            image={GreyMatterLogo}
            imageWidth={150}
            imageHeight={180}
            header="Student success links"
            height={680}
          >
            <LinksWidget linksData={linksData} />
          </WidgetTile>

          {/* <WidgetTile
            image={WorkdayLogo}
            imageWidth={150}
            imageHeight={180}
            header="Upcoming PTO"
            height={680}
          >
            <MyAbsences filteredData={filteredData} />
          </WidgetTile> */}
          {/* <WidgetTile
            image={WorkdayLogo}
            imageWidth={150} imageHeight={180}
            header={'Workday Employment Info'}
          >
            <WorkdayEmployment
              dataverseData={dataverseData}
              userSupervisor={userSupervisor}
              userName={userProfile?.displayName}
            />
          </WidgetTile> */}
          {/* <ConditionalDisplay condition={userProfile?.email != "<EMAIL>"}>            
          </ConditionalDisplay> */}
          {/* <ConditionalDisplay
            condition={userProfile?.email != "<EMAIL>"}
          >
            <WidgetTile
              image={CSNLogo}
              imageHeight={210}
              imageWidth={190}
              header="Course Catalog Summary"
            >
              <MyCSN filteredData={filteredData} />
            </WidgetTile>
          </ConditionalDisplay> */}
          {/* <WidgetTile
            image={WorkdayLogo}
            imageWidth={150}
            imageHeight={180}
            header={"My Team Absences"}
          >
            <MyAbsences dataverseData={dataverseData} />
          </WidgetTile> */}
          <WidgetTile
            image={getDynamicPapyrusLogo()}
            imageWidth={150}
            imageHeight={180}
            header={"My Submissions"}
          >
            <PapyrusForms />
          </WidgetTile>
          <WidgetTile
            image={getDynamicPapyrusLogo()}
            imageWidth={150}
            imageHeight={180}
            header={"My Signatures"}
          >
            <MySignatures />
          </WidgetTile>
          {/* <WidgetTile
            image={PapyrusFlower}
            imageWidth={150}
            imageHeight={180}
            header={"My Evaluation"}
          >
            <MyPerformance />
          </WidgetTile> */}
          <WidgetTile
            image={getDynamicPapyrusLogo()}
            imageWidth={150}
            imageHeight={180}
            header={"Routing Approvals"}
          >
            <RoutingApprovals />
          </WidgetTile>
          {/* <WidgetTile
            image={WorkdayLogo}
            imageWidth={150}
            imageHeight={180}
            header={"Invoice"}
          >
            <Invoice />
          </WidgetTile> */}
          {/* <WidgetTile
            image={WorkdayLogo}
            imageWidth={150}
            imageHeight={180}
            header={"HR Requisition"}
          >
            <HRRequisition />
          </WidgetTile> */}
          {/* <WidgetTile
            image={WorkdayLogo}
            imageWidth={150}
            imageHeight={180}
            header={"Purchase Requisition"}
          >
            <PurchaseRequisition />
          </WidgetTile> */}
          {/* <WidgetTile
            image={WorkdayLogo}
            imageWidth={150}
            imageHeight={180}
            header={"Purchase Order"}
          >
            <PurchaseOrder />
          </WidgetTile> */}
          <WidgetTile
            image={getDynamicLogo()}
            imageWidth={210}
            imageHeight={190}
            header={"Help Desk Support"}
          >
            <HelpDesk />
          </WidgetTile>
        </ConditionalDisplay>
        <ConditionalDisplay
          condition={userProfile?.email == "<EMAIL>"}
        >
          <WidgetTile
            image={GreyMatterLogo}
            imageWidth={160}
            imageHeight={160}
            header="Student Appointments"
            italics={true}
            actionButton={{
              label: "View in app",
              url: "https://greymatter-preprod.crm.dynamics.com/main.aspx?pagetype=webresource&webresourceName=msdyn_/ScheduleBoard/index.html?data={\"tab\"%3A\"d48c2e19-70d7-ef11-8ee9-6045bdd5d315\"%2C\"startdate\"%3A\"2025-01-20\"%2C\"viewmode\"%3A\"hourly\"%2C\"viewtype\"%3A\"gantt\"%2C\"map\"%3A\"false\"%2C\"columnwidth\"%3A\"50\"}"
            }}
          >
            <AppointmentsWidget appointmentsData={appointmentData}
              userName={userProfile?.displayName} />
          </WidgetTile>
          <WidgetTile
            image={getDynamicPapyrusLogo()}
            imageWidth={150}
            imageHeight={180}
            header={"Form Approvals"}
          >
            <PapyrusApprovals />
          </WidgetTile>
          <WidgetTile
            image={WorkdayLogo}
            imageWidth={150}
            imageHeight={180}
            header={"Workday Employment Info"}
            height={680}
          >
            <WorkdayEmployment
              transferHireData={transferHireData}
              workdayData={workdayData}
              absenseData={absenseData}
              dataverseData={dataverseData}
              userSupervisor={userSupervisor}
              userName={userProfile?.displayName}
            />
            <div>
            </div>
          </WidgetTile>
          <WidgetTile
            image={getDynamicPapyrusLogo()}
            imageWidth={150}
            imageHeight={180}
            header={"Performance Review"}
            height={680}
          >
            <PerformanceReview userName={userProfile?.displayName} />
          </WidgetTile>
          <WidgetTile
            image={getDynamicPapyrusLogo()}
            imageWidth={150}
            imageHeight={180}
            header={"Employee Onboarding"}
            height={680}
          >
            <EmployeeOnboarding userName={userProfile?.displayName} />
          </WidgetTile>
          <WidgetTile
            image={getDynamicPapyrusLogo()}
            imageWidth={150}
            imageHeight={180}
            header={"Policies and Procedures"}
            height={680}
          >
            <PoliciesProcedures linksData={linksData} />
          </WidgetTile>
          <WidgetTile
            image={getDynamicLogo()}
            imageWidth={190}
            imageHeight={190}
            header={"Faculty & Staff Resources"}
          >
            <Resources />
          </WidgetTile>
          {/* <WidgetTile
            image={GreyMatterLogo}
            imageWidth={150}
            imageHeight={180}
            header="Student success links"
            height={680}
          >
            <LinksWidget linksData={linksData} />
          </WidgetTile> */}
          <WidgetTile
            className={styles.testingTile}
            imageHeight={210}
            imageWidth={190}
            image={getDynamicLogo()}
            header={"Events, Academic Calendar & Holidays"}
          >
            <HolidayCalendarWidget holidayCalenderData={holidayCalenderData} />
          </WidgetTile>
        </ConditionalDisplay>
      </div>
    </div>
  );
};
