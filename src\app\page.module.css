.timeline {
  align-items: baseline;
}

.backgroundContainer {
  position: relative;
  width: 100%;
  height: 95vh;
}

.searchCalendar {
  height: 10%;
  display: flex;
  /* margin: 100px 0px 0px 0px; */
  justify-content: space-between;
  /* background-image: url(../../public/NewIcons/header_bckgd.png); */
  background-repeat: no-repeat;
  background-size: 100%;
  background-position: top;
  /* padding: 10px 132px; */
  align-items: center;
}

.welcomeText {
  display: flex;
  align-items: center;
}


.searchContainer {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
  padding: 23px 200px 15px 200px;
  background-color: #f8f9fb;

  @media (max-width: 768px) {
    padding: 20px 20px 20px 20px;
  }
}

@media (max-width: 768px) {
  .searchCalendar {
    display: flex;
    flex-direction: column;
    height: 14%;
    background-size: 100% 100%;
    margin: 57px 0px 0px 0px;
  }
}

.mainWrapper {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.contentWrapper {
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.overlayContent {
  position: relative;
  width: 100%;
  flex: 1;
  z-index: 1;
}

.mainContainer {
  padding: 25px;
  /* background-color: #ced5e111;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px); */
  background-color: #fff;
  transition: filter 0.3s ease;
}

.blurred {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background-color: #f8f9fb;
  filter: blur(10px);
  pointer-events: none;
  padding: 25px;
}

.portalContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 6rem;
  justify-content: center;
  align-items: flex-start;
  padding: 20px;
  max-height: 44rem;
  overflow-y: scroll;
  border-radius: 10px;
  scrollbar-width: thin;
  scrollbar-color: #555 #f5f5f500;
  height: calc(100vh - 262px);
}

.portalContainer::-webkit-scrollbar {
  width: 8px;
  background-color: #ced5e111;
  backdrop-filter: blur(10px);
}

.portalContainer::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  background-color: #ced5e111;
  backdrop-filter: blur(10px);
}

.portalContainer::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: #c6c6c649;
}

.portalImage {
  height: 65%;
  width: fit-content;
  align-items: center;
  margin-top: 50px;
  display: flex;
}

.portalName {
  height: 35%;
  width: 100%;
  display: flex;
  justify-content: center;
}

.tileContainer {
  position: relative;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 20px;
  width: 250px;
  height: 250px;
  cursor: pointer;
  overflow: hidden;
  transition: transform 0.3s ease;
  box-shadow: 0px 3px 20px 2px rgba(0, 0, 0, 0.06);
  border: 0.5px solid #00489078;
}

.tileContainer:hover {
  transform: scale(1.08);
  box-shadow: 0px 3px 20px 2px rgba(0, 0, 0, 0.06);
}

.tileContainer:hover .tileHoverOverlay::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  backdrop-filter: blur(3px);
  z-index: 0;
  border-radius: 10px;
  border: 1px solid #004890;
}

.tileHoverOverlay {
  display: flex;
  flex-direction: column;
  position: absolute;
  top: 15px;
  left: 15px;
  right: 15px;
  bottom: 50px;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: opacity 0.3s, transform 0.3s ease;
  overflow: hidden;
}

.labelOutside {
  display: block;
  position: absolute;
  bottom: 25px;
  left: 50%;
  transform: translateX(-50%) translateY(10px);
  opacity: 0;
  transition: opacity 0.3s ease, transform 0.3s ease;
  z-index: 2;
  width: 100%;
  text-align: center;
  color: var(--secondaryColor);
  font-size: 18px;
  font-weight: 500;
  top: 84%;
}

.tileContainer:hover .labelOutside {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
}

.tileContainer:hover .appOpenImage {
  display: block;
}

.appOpenImage {
  top: 0;
  left: 0;
  z-index: 3;
  display: none;
}

.nameLabel {
  font-size: 20px;
  color: #0c1c63;
  align-self: center;
}

.iconAlignment {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
}

.starIcon {
  color: #0c1c63;
  font-size: 20px;
}

.starIconFilled {
  color: #ffd700;
  font-size: 20px;
}

.tileName {
  font-size: 18px;
  color: var(--secondaryColor);
  margin-top: 14px;
}

.welcomeHeader {
  font-size: 30px;
  color: #fff;
  margin: 0px;
  font-weight: 500;

  @media (max-width: 768px) {
    font-size: 16px;
    width: max-content;
  }
}