"use client";
import { useContext, useRef, useState, useEffect } from "react";
import { UserProfileContext } from "../../contexts/UserProfileContext";
import { UserImageContext } from "../../contexts/UserImageContext";
import SauLogo from "../../public/SSOImages/SSOTransparentWhite.webp";
import SauLogoNew from "../../public/SSOImages/logo_whiteNew.svg";
import CSNLogo from "../../public/SSOImages/CSNLogo.png";
import styles from "./Navbar.module.css";
import Image from "next/image";
import {
  AuthenticatedTemplate,
  UnauthenticatedTemplate,
  useMsal,
} from "@azure/msal-react";
import { SignInButton } from "../SignInButton";
import { Tab, TabList } from "../Menu/Menu";
import EdPortalBlue from "../../public/SSOImages/WidgetsBlue.svg";
import EdPortalRed from "../../public/SSOImages/WidgetsRed.svg";
import MyPortalRed from "../../public/SSOImages/PortalRed.svg";
import MyPortalBlue from "../../public/SSOImages/PortalBlue.svg";
import Notification from "../../public/SSOImages/Notifi_new.svg";
import CommonDP from "../../public/SSOImages/CommonDP.svg";
import { NotificationItem } from "../Menu/Menu";
import { Paginator } from "primereact/paginator";
import { OverlayPanel } from "primereact/overlaypanel";
import { Button } from "primereact/button";
import { DropdownProfile } from "../DropdownProfile/DropdownProfile";
import simpleLogo from "../../public/NewIcons/University_Logo.svg";
import MyPortalWhite from "../../public/NewIcons/My_portal.svg";
import MyAppsWhite from "../../public/NewIcons/My_Apps.svg";
import EDPortalWhite from "../../public/NewIcons/EDportal_Apps.svg";
import { getDynamicHeaderLogo } from "../../utilities/LogoMap/logoMapping";

export const Navbar = ({ activeTab, setActiveTab, notificationsMockData }) => {
  const { instance } = useMsal();
  const notificationPanelRef = useRef(null);
  const signOutPanelRef = useRef(null);
  const menuRef = useRef(null);
  const userProfile = useContext(UserProfileContext);
  const userImage = useContext(UserImageContext);
  const { email, displayName, role, tenantRoles, jobTitle } = userProfile;
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggleNotifications = (event) => {
    notificationPanelRef.current.toggle(event);
  };

  const handleLogout = () => {
    instance.logoutPopup();
  };

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setIsMobileMenuOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <NavbarContainer>
      <div className={styles.navbarContent} ref={menuRef}>
        {/* <Image src={simpleLogo} alt="SAU Logo" className={styles.headerLogo} /> */}
        <Image src={getDynamicHeaderLogo()} alt="Logo" width={220} height={55} />
        <ProfileContainer>
          <button
            className={styles.menuToggle}
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            <i
              className="pi pi-bars"
              style={{ color: "#fff", fontSize: "22px" }}
            />
          </button>
          <div
            className={`${styles.menuItems} ${isMobileMenuOpen ? styles.show : ""
              }`}
          >
            <AuthenticatedTemplate>
              <TabList>
                <Tab
                  value={"widget"}
                  activeTab={activeTab}
                  label={"My Portal"}
                  iconActive={
                    <Image
                      width={18}
                      height={18}
                      src={MyPortalWhite}
                      alt="My Portal"
                    />
                  }
                  iconInactive={
                    <Image
                      width={18}
                      height={18}
                      src={MyPortalWhite}
                      alt="My Portal"
                    />
                  }
                  setActiveTabIndex={setActiveTab}
                />
                <Tab
                  value={"myEdPortals"}
                  activeTab={activeTab}
                  label={"My Apps"}
                  iconActive={
                    <Image
                      width={20}
                      height={20}
                      src={EDPortalWhite}
                      alt="My Apps"
                    />
                  }
                  iconInactive={
                    <Image
                      width={20}
                      height={20}
                      src={EDPortalWhite}
                      alt="My Apps"
                    />
                  }
                  setActiveTabIndex={setActiveTab}
                />
                <Tab
                  value={"edPortalsApp"}
                  activeTab={activeTab}
                  label={"EdPortal Apps"}
                  iconActive={
                    <Image
                      width={20}
                      height={20}
                      src={MyAppsWhite}
                      alt="My Apps"
                    />
                  }
                  iconInactive={
                    <Image
                      width={20}
                      height={20}
                      src={MyAppsWhite}
                      alt="My Apps"
                    />
                  }
                  setActiveTabIndex={setActiveTab}
                />
              </TabList>
              <div className={styles.rightsecWrap}>
                <Image
                  src={Notification}
                  alt="Notification Logo"
                  width={18}
                  height={18}
                  onClick={toggleNotifications}
                />
                <OverlayPanel
                  ref={notificationPanelRef}
                  id="overlay_panel"
                  style={{ width: "500px" }}
                >
                  <h3 className={styles.notificationHeader}>Recent News</h3>
                  {notificationsMockData.map((notification, index) => (
                    <NotificationItem key={index} item={notification} />
                  ))}
                  <Paginator first={0} rows={5} totalRecords={10} />
                </OverlayPanel>
                <div className={styles.profileWrap}>
                  <Name name={displayName} />
                  <Role
                    role={role?.name}
                    jobTitle={
                      displayName === "Berkley Esherwood"
                        ? "Data Analyst - Student Advising"
                        : jobTitle
                    }
                  />{" "}
                  {/* This condition can be deleted once Esherwood's jobtitle is replaced in the backend. */}
                </div>
                <DropdownProfile
                  userProfile={userProfile}
                  userImage={userImage?.imageUrl}
                  handleLogout={handleLogout}
                />
              </div>
            </AuthenticatedTemplate>
            <UnauthenticatedTemplate>
              <SignInButton />
            </UnauthenticatedTemplate>
          </div>
        </ProfileContainer>
      </div>
    </NavbarContainer>
  );
};

const NavbarContainer = ({ children }) => {
  return <div className="navbarContainer">{children}</div>;
};

const ProfileContainer = ({ children }) => {
  return <div className={styles.profileContainer}>{children}</div>;
};

const Name = ({ name }) => {
  return (
    <div className={styles.nameContainer}>
      <div>{name}</div>
    </div>
  );
};

const Role = ({ role, jobTitle }) => {
  return (
    <div className={styles.roleContainer}>
      <div>
        {role} {jobTitle && `| ${jobTitle}`}{" "}
      </div>
    </div>
  );
};
