.portalContainer {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(30%, 1fr));
  grid-gap: 20px;
  justify-items: start;
  /* margin: 0px 106px 20px 120px; */
  margin: 0px 166px 0px 180px;
  padding: 0 20px 20px 20px;
  background-color: #f8f9fb;
  /* max-height: 81vh; */
  overflow-y: scroll;
  overflow-x: none;
  border-radius: 0px;
  scrollbar-width: thin;
  scrollbar-color: #55555533 #f5f5f500;
  height: calc(100vh - 212px);
  /* background: rgba(255, 255, 255, 0.36);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px); */
}

@media (max-width: 1500px) {
  .portalContainer {
    grid-template-columns: repeat(auto-fill, minmax(45%, 1fr));
  }
}

@media (max-width: 950px) {
  .portalContainer {
    grid-template-columns: repeat(auto-fill, minmax(49%, 1fr));
    margin: 0px 2px 20px 2px;
  }
}

.portalContainer::-webkit-scrollbar {
  width: 8px;
  background-color: #ced5e111;
  backdrop-filter: blur(10px);
}

.portalContainer::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  background-color: #ced5e111;
  backdrop-filter: blur(10px);
}

.portalContainer::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
  background-color: #f9f5eebb;
}

.blurBackground {
  /* backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background-color: #ced5e111; */
  background-color: #f8f9fb;
  transition: filter 0.3s ease;
}

.blurred {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background-color: #f8f9fb;
  filter: blur(10px);
  pointer-events: none;
}

.sharedContainer {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
}

/* GradeWidget */

.tableHeader {
  color: #002868;
  border-bottom: none;
  font-weight: 600;
  font-size: 16px;
  padding-bottom: 10px;
  text-align: left;
  width: 20%;

  @media (max-width: 768px) {
    font-size: 12px;
  }
}

.italics {
  font-style: italic;
}

.tableData {
  color: var(--secondaryColor);
  padding: 4px 0px;
  font-size: 14px;
}

/* Specifically target the first child of any element with the class 'tableHeader' */
.tableHeader:first-child {
  width: 28%;
}

.tableHeader:nth-child(2) {
  width: 50%;
}


.gradeRow {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: 20px;
  align-items: center;
}

.widgetHeader {
  padding-left: 10px;
  font-size: 20px;
  font-weight: 600;
  color: var(--primaryColor);
  border-bottom: 1px solid var(--primaryColor);
}

.gradeIconLabel {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
}

.gradesContainer {
  display: flex;
  flex-direction: column;
  /* padding: 0px 10px 10px 10px; */
}

.gradePercentage {
  font-weight: 600;
}

/* Schedule Widget */

.classScheduleHeader {
  width: 50%;
  font-weight: 700;
  color: var(--primaryColor);
}

.subjectContainer {
  width: 50%;
  gap: 5px;
  display: flex;
  flex-direction: column;
  color: var(--secondaryColor);
}

.detailsContainer {
  width: 60%;
  font-size: 14px;
  color: var(--secondaryColor);
}

.subjectHeader {
  font-size: 20px;
  font-weight: 600;
}

.dateTime {
  font-size: 17px;
  font-weight: 600;
  color: gray;
}

.detailsContainer {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.boldedDetail {
  font-weight: 600;
  padding-right: 10px;
}

/* Upcoming Widget */

.columnContainer {
  display: flex;
  flex-direction: column;
  flex: 1 0 calc(50% - 20px);
  justify-content: space-between;
  margin-bottom: 20px;
}

.holidayContainer {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.workdayContainer {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  border-bottom: 1px solid var(--primaryColor);
  color: var(--primaryColor);
}

.workdayHeader {
  font-size: 16px;
  font-weight: 600;
}

.workdayHeaderResult {
  font-weight: 400;
  color: var(--secondaryColor);
  font-size: 14px;
}

.helpDeskContent {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-top: 30px;
}

.linksContainer {
  display: flex;
  gap: 20px;
  flex-direction: column;
  margin: 2% 0%;
}

.linksContainer p {
  text-decoration: underline;
  font-size: 16px;
  color: blue;
}

.widget-container {
  width: 300px;
  margin: 0 auto;
  border: 1px solid #ccc;
  padding: 10px;
  min-height: 200px;
  background-color: #f9f9f9;
}

.widget-tile {
  padding: 10px;
  margin: 5px 0;
  background-color: #e0e0e0;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: move;
}

.subHeader {
  padding-bottom: 10px;
  font-size: 16px;
  font-weight: 700;
  color: #004890;
}

.dataTableHeader {
  position: sticky;
  top: 0;
  height: 20px;
  background-color: #fff;
  border-bottom: 1px solid #004890;
  z-index: 1;
}

.widgetTable tr {
  position: relative;
  margin-top: 10px;
}

.textOverflow {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 160px;
}



/* Floating AI */
.chat_floating_button {
  position: fixed;
  bottom: 40px;
  right: 40px;
  background-color: #fddb3a;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  box-shadow: 0px 3px 20px 2px rgba(0, 0, 0, 0.06);
  z-index: 9999;
  /* cursor: url('../../public/SSOImages/cursor-hand.svg'), pointer; */
}

.chat_icon {
  width: 100%;
  height: 100%;
}

.overlayText {
  font-size: 12px;
  /* font-weight: 500; */
  color: #004990;
  position: absolute;
  top: 50%;
  left: 58%;
  transform: translate(-50%, -50%);
  width: 74%;
}

/* Chat Popup */
.chat_popup {
  position: fixed;
  bottom: 90px;
  right: 108px;
  width: 424px;
  background-color: white;
  /* border: 1px solid #ccc; */
  border-radius: 0px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  z-index: 1000;
}

.chat_header {
  background-color: #004990;
  color: white;
  padding: 10px 22px;
  border-radius: 0px 0px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 86px;
}

.closeButton {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
}

.chatBody {
  padding: 15px 20px;
  height: 500px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  gap: 10px;
}

.bot_message {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 10px;
}

.headerTitle {
  font-size: 18px;
  font-weight: 600;
  color: #fff;
}

.sendMessage {
  font-size: 15px;
  color: #004990;
  width: 88%;
}

.sendMessageCont {
  background-color: #f2f2f2;
  padding: 14px 18px;
  width: 98%;
  border-radius: 10px;
}

.receiveMessageCont {
  background-color: #fdfbe0;
  padding: 14px 18px;
  width: 98%;
  border-radius: 10px;
}

.messageLastseen {
  font-size: 12px;
  color: #969698;
  padding: 4px 0;
}

.bot_icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
}

.bot_dp {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.bot_receiveDp {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.chat_footer {
  display: flex;
  padding: 10px;
  border-top: 1px solid #ccc;
}

.message_input {
  flex: 1;
  padding: 10px;
  border-right: 1px solid #ccc;
  border-radius: 0px;
}

.send_button {
  color: #004990;
  border: none;
  border-radius: 5px;
  padding: 10px 15px;
  cursor: pointer;
}



.typing_indicator {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 5px;
}

.typing_indicator span {
  width: 8px;
  height: 8px;
  background-color: #55555561;
  /* Replace with your desired dot color */
  border-radius: 50%;
  animation: typing 1.5s infinite ease-in-out;
}

.typing_indicator span:nth-child(1) {
  animation-delay: 0s;
}

.typing_indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing_indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {

  0%,
  80%,
  100% {
    transform: scale(0);
  }

  40% {
    transform: scale(1);
  }
}