import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";

export const PurchaseRequisition = ({}) => {
  const mockData = [
    {
      id: 98765,
      title: "Printing Toner",
      totalAmount: 345.78,
      department: "Registrar",
      lastUpdated: "7/29/2024",
    },
    {
      id: 98763,
      title: "Laptop",
      totalAmount: 234.56,
      department: "Registrar",
      lastUpdated: "7/31/2024",
    },
    {
      id: 98766,
      title: "Office Chairs",
      totalAmount: 1200.99,
      department: "Human Resources",
      lastUpdated: "8/01/2024",
    },
    {
      id: 98767,
      title: "Projector",
      totalAmount: 789.50,
      department: "IT",
      lastUpdated: "8/03/2024",
    },
    {
      id: 98768,
      title: "Whiteboards",
      totalAmount: 450.00,
      department: "Training",
      lastUpdated: "8/05/2024",
    },
    {
      id: 98769,
      title: "Mobile Phones",
      totalAmount: 2300.45,
      department: "Sales",
      lastUpdated: "8/07/2024",
    },
    {
      id: 98770,
      title: "Marketing Materials",
      totalAmount: 1500.00,
      department: "Marketing",
      lastUpdated: "8/10/2024",
    },
  ];
  
  return (
    <DataTable
      value={mockData}
      lazy
      columnResizeMode="expand"
      dataKey="id"
      paginator
      first={0}
      rows={10}
      globalFilterFields={[]}
      selectionMode="single"
      >
      <Column
        field="id"
        header="Req ID"
      />
      <Column
        field="title"
        header="Title"
      />
      <Column
        field="totalAmount"
        header="Total Amount"
      />
      <Column
        field="department"
        header="Department"
      />
      <Column
        field="lastUpdated"
        header="Last Updated"
      />
    </DataTable>
  );
}