/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./utilities/**/*.{js,ts,jsx,tsx,mdx}",
    "./hooks/**/*.{js,ts,jsx,tsx,mdx}",
    "./contexts/**/*.{js,ts,jsx,tsx,mdx}",
  ],

  // Enable JIT mode for faster builds
  mode: "jit",
  theme: {
    extend: {
      colors: {
        "color-glass-white": "#f9f5ee4e",
        "color-frosted-white": "#ffffff80",
        "color-navy-blue": "#002868",
        "color-dark-blue": "#004990",
        "color-dark-red": "#004890",
        "color-error-red": "#FF2300",
        "color-light-gray": " #ced5e111",
        "color-soft-gray": "#f2f2f2",
        "color-soft-yellow": "#fdfbe0",
        "color-soft-red": "#FFE8E5",
        "color-steel-gray": "#969698",
        "color-white": "#fff",
        "color-red-search": "#00489099",
      },
      fontFamily: {
        "open-sans": ["Open Sans", "sans-serif"],
        georgia: ["Georgia", "serif"],
      },
      boxShadow: {
        "custom-light": "0 4px 10px rgba(0, 0, 0, 0.2)",
      },
      scrollbarWidth: {
        thin: "thin",
      },
    },
  },
  plugins: [],
};
