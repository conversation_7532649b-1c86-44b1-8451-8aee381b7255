import { useEffect, useState } from "react";
import { getAccessTokenForScope } from "../utilities/Msal/GetAccessTokenForScope";
import { formBuilderApiRequest } from "../utilities/Msal/msalConfig";

const api = process.env.NEXT_PUBLIC_FORM_BUILDER_API_DEMO;

export const defaultTileValues = {
  draftsCount: 0,
  recalledCount: 0,
  approvedCount: 0,
  rejectedCount: 0,
  cancelledCount: 0,
  inProgressCount: 0,
  onHoldCount: 0,
};

export const useSubmissionTileValues = ({
  solutionId,
  fetchUserTileValues = true,
  fetchAllTileValues = true,
}) => {
  const [userTileValues, setUserTileValues] = useState(defaultTileValues);
  const [allTileValues, setAllTileValues] = useState(defaultTileValues);

  useEffect(() => {
    const fetchMySubmissionValues = async () => {
      try {
        const accessToken = await getAccessTokenForScope(formBuilderApiRequest);
        const response = await fetch(
          `${api}FormSubmission/MySubmissionDashboard?solutionId=${solutionId}`,
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
              "next-action": "MySubmissionDashboard",
            },
          }
        );

        const json = await response.json();
        setUserTileValues(json);
      } catch (error) {
        console.error("Failed to fetch my submission values:", error);
      }
    };

    const fetAllSubmissionValues = async () => {
      try {
        const accessToken = await getAccessTokenForScope(formBuilderApiRequest);

        const response = await fetch(
          `${api}FormSubmission/AllSubmissionDashboard?solutionId=${solutionId}`,
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
              "next-action": "AllSubmissionDashboard",
            },
          }
        );

        const json = await response.json();
        setAllTileValues(json);
      } catch (error) {
        console.error("Failed to fetch status counts:", error);
      }
    };

    if (fetchUserTileValues) fetchMySubmissionValues();
    if (fetchAllTileValues) fetAllSubmissionValues();
  }, [solutionId, fetchUserTileValues, fetchAllTileValues]);

  return { userTileValues, allTileValues };
};
