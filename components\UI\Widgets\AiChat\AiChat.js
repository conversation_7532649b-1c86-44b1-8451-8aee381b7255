import React, { useState, useEffect, useRef, useContext } from "react";
import styles from "../../../WidgetDashboard/WidgetDashboard.module.css";
import chatIcon from "../../../../public/SSOImages/CommonDP.svg";
import Image from "next/image";
import send from "../../../../public/SSOImages/Send_chatNew.svg";
import botIcon from "../../../../public/SSOImages/Chatbot_icon.svg";
import whiteDp from "../../../../public/SSOImages/chat_bot.svg";
import commonDp from "../../../../public/SSOImages/chat_bot.svg";
import receiveDp from "../../../../public/SSOImages/receive-dp.svg";
import welcome from "../../../../public/SSOImages/welcome_withoutText.svg";
import { UserProfileContext } from "../../../../contexts/UserProfileContext";
import { useApi } from "../../../../hooks/useApi";
import { ConditionalDisplay } from "../../../ConditionalDisplay/ConditionalDisplay";
import { headers } from "../../../../next.config";
import { formatToTimeStampFromDate } from "../../../../utilities/Msal/dateFormat";

const llmServerApi = process.env.NEXT_PUBLIC_LLM_SERVER_API;

export const AiChart = () => {
  const { loading, callApi } = useApi();
  const [isOpen, setIsOpen] = useState(false);
  const [iconSrc, setIconSrc] = useState(welcome);

  const popupRef = useRef(null);
  const chatEndRef = useRef(null);
  const userProfile = useContext(UserProfileContext);
  const { displayName, userID } = userProfile;

  const [chatData, setChatData] = useState([]);
  const [newMessage, setNewMessage] = useState("");

  const chatWithBot = async (question) => {
    const chatWithBotUrl = `${llmServerApi}chatWithBot?userQuestion=${question}`;

    const chatWithBotParems = {
      method: "GET",
      url: chatWithBotUrl,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Content-Type": "application/json",
      },
      credentials: "include",
    };

    const result = await callApi(chatWithBotParems);
    if (result) {
      const botAnswer = result.data.botAnswer;
      if (chatData.some((chat) => chat.isErrorMessage)) {
        setChatData((prevChatData) =>
          prevChatData.filter((chat) => !chat.isErrorMessage)
        );
      }
      addToChat(botAnswer, true, false);
    } else {
      addToChat("Something went wrong!", true, true);
    }
  };

  const renderMessageWithLinks = (message) => {
    return message.split(/\s+/).map((word, index) => {
      if (/\bhttps?:\/\/\S+/gi.test(word)) {
        return (
          <a
            key={index}
            href={word}
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-500 underline"
          >
            {word}
          </a>
        );
      }
      return `${word} `;
    });
  };

  // const messageData = [
  //   {
  //     message: "How do I get my ratings?",
  //     isBotResponse: false,
  //   },
  //   {
  //     message:
  //       "You can view your ratings in the student dashboard under 'My Profile' section.",
  //     isBotResponse: true,
  //   },

  //   {
  //     message: "How can I reset my password?",
  //     isBotResponse: false,
  //   },
  //   {
  //     message:
  //       "To reset your password, go to the login page and click on 'Forgot Password'..",
  //     isBotResponse: true,
  //   },

  //   {
  //     message: "Where can I find my assignments?",
  //     isBotResponse: false,
  //   },
  //   {
  //     message:
  //       "Assignments are available under the 'Assignments' tab in your dashboard.",
  //     isBotResponse: true,
  //   },

  //   {
  //     message: "How do I contact support?",
  //     isBotResponse: false,
  //   },
  //   {
  //     message:
  //       "You can contact support via the 'Help & Support' section in the application.",
  //     isBotResponse: true,
  //   },
  //   {
  //     message: "Can I update my profile details?",
  //     isBotResponse: false,
  //   },
  //   {
  //     message:
  //       "Yes, you can update your profile details from the 'Edit Profile' option in your dashboard.",
  //     isBotResponse: true,
  //   },
  // ];

  const toggleChat = () => {
    setIsOpen((prev) => !prev);
    // setIconSrc((prev) => (prev === welcome ? botIcon : welcome));
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (popupRef.current && !popupRef.current.contains(event.target)) {
        setIsOpen(false);
        // setIconSrc(welcome);
      }
    };

    if (isOpen) {
      document.addEventListener("click", handleClickOutside);
    } else {
      document.removeEventListener("click", handleClickOutside);
    }

    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, [isOpen]);

  const addToChat = (message, isBotResponse, isErrorMessage) => {
    setChatData((prevChatData) => [
      ...prevChatData,
      {
        message: message,
        isBotResponse: isBotResponse,
        isErrorMessage,
        time: new Date(),
      },
    ]);
  };

  useEffect(() => {
    if (displayName) {
      addToChat(
        `Hi there, ${displayName}! 👋 I'm Papyrus Chatbot!, your virtual assistant. It looks like it's your first time here. How can I help you today? 😊`,
        true,
        false,
        new Date()
      );
    }
  }, [displayName]);

  const handleSendMessage = () => {
    if (newMessage.trim() === "") return;

    addToChat(newMessage, false, false);
    chatWithBot(newMessage);
    setNewMessage("");
  };

  useEffect(() => {
    if (chatEndRef.current) {
      chatEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [chatData]);

  return (
    <div>
      <div className={styles.chat_floating_button} onClick={toggleChat}>
        <Image src={isOpen ? botIcon : welcome} className={styles.chat_icon} />
        {!isOpen && (
          <span className={styles.overlayText}>
            {"Welcome to Papyrus..!! I'm here to"}
            <br /> {"help you find your way. Let's chat!"}
          </span>
        )}
      </div>
      {isOpen && (
        <div
          className={
            "fixed bg-white rounded-none flex flex-col z-[1000] shadow-custom-light w-[22%] right-[5.5%] bottom-[14%]"
          }
          ref={popupRef}
        >
          <div className="bg-color-dark-blue text-white rounded-none flex justify-between items-center h-[5.25rem] py-2.5 px-5">
            <span className="flex flex-row items-center gap-2.5">
              <Image
                src={whiteDp}
                alt="Bot Icon"
                className="w-12 h-12 rounded-[50%]"
              />
              <h3 className="text-white font-semibold text-lg">
                Papyrus Chatbot
              </h3>
            </span>
            <button
              className="bg-none border-0 text-white text-lg cursor-pointer"
              onClick={toggleChat}
            >
              <i className="pi pi-chevron-down" />
            </button>
          </div>
          <div className="flex flex-col justify-end gap-2.5 py-4 px-5 h-[55vh] ">
            <div className="overflow-y-scroll scrollbar-thin">
              {chatData.map((data, index) => (
                <React.Fragment key={index}>
                  <div
                    className={`${
                      data.isBotResponse ? "mb-4" : "flex-row-reverse pr-3 mb-4"
                    } flex items-start gap-3 `}
                  >
                    {!data.isErrorMessage && (
                      <Image
                        src={data.isBotResponse ? commonDp : receiveDp}
                        alt="Bot Icon"
                        className="w-10 h-10 rounded-[50%]"
                      />
                    )}
                    <div
                      className={`flex flex-col ${
                        data.isErrorMessage ? "mx-auto" : ""
                      }`}
                    >
                      <span
                        className={`${
                          data.isErrorMessage
                            ? "bg-color-soft-red w-[15vw] text-center"
                            : data.isBotResponse
                            ? "bg-color-soft-gray w-[95%]"
                            : "bg-color-soft-yellow w-[98%]"
                        } py-3.5 px-5  rounded-[10px]`}
                      >
                        <p
                          className={`${
                            data.isErrorMessage
                              ? "text-color-error-red"
                              : "text-color-dark-blue w-[98%]"
                          } text-[15px]`}
                        >
                          {data.isBotResponse
                            ? renderMessageWithLinks(data.message)
                            : data.message}
                        </p>
                      </span>
                      {!data.isErrorMessage && (
                        <h6
                          className={`${
                            data.isBotResponse ? "mr-0" : " mr-3 text-end"
                          } text-color-steel-gray text-xs py-1 px-0`}
                        >
                          {data.isBotResponse
                            ? `Papyrus Chatbot . ${formatToTimeStampFromDate(
                                data.time.toString()
                              )}`
                            : ` You . ${formatToTimeStampFromDate(
                                data.time.toString()
                              )}`}
                        </h6>
                      )}
                    </div>
                  </div>
                </React.Fragment>
              ))}
              <ConditionalDisplay condition={loading}>
                <React.Fragment>
                  <div className={"mb-4 flex items-start gap-3"}>
                    <Image
                      src={commonDp}
                      alt="Bot Icon"
                      className="w-10 h-10 rounded-[50%]"
                    />
                    <div className="flex flex-col">
                      <span
                        className={
                          "bg-color-soft-gray w-[95%] py-3.5 px-5  rounded-[10px]"
                        }
                      >
                        <div className={styles.typing_indicator}>
                          <span></span>
                          <span></span>
                          <span></span>
                        </div>
                      </span>
                    </div>
                  </div>
                </React.Fragment>
              </ConditionalDisplay>
              <div ref={chatEndRef} />
            </div>
            <div className="border-t border-gray-300 flex p-2.5">
              <input
                type="text"
                className="border-r border-gray-300 grow p-2.5 rounded-none"
                placeholder="Type your message"
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === "Enter") handleSendMessage();
                }}
              />
              <button
                className="border-0 text-color-dark-blue rounded-[5px] py-2.5 px-5 cursor-pointer"
                onClick={handleSendMessage}
              >
                <Image src={send} width={24} height={24} />
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
