"use client";
import React, { useState, useEffect, createContext } from "react";
import { useMsal, useAccount } from "@azure/msal-react";
import { formBuilderApiRequest } from "../utilities/Msal/msalConfig";
import { getAccessTokenForScope } from "../utilities/Msal/GetAccessTokenForScope";

const UserProfileContext = createContext();

const api = process.env.NEXT_PUBLIC_FORM_BUILDER_API;

const UserProfileContextProvider = ({ children }) => {
  const { accounts } = useMsal();
  const account = useAccount(accounts[0] ?? {});
  const { username } = accounts.length > 0 ? accounts[0] : {};

  const [contextData, setContextData] = useState({});

  useEffect(() => {
    const loadUserProfileData = async () => {
      try {
        const accessToken = await getAccessTokenForScope(formBuilderApiRequest);
        const response = await fetch(`${api}UserProfile/${username}`, {
          method: "GET",
          headers: {
            Accept: "*/*",
            Authorization: `Bearer ${accessToken}`,
            "next-action": "UserProfile",
          },
        });

        if (!response.ok) {
          throw new Error(`Error: ${response.status}`);
        }

        const data = await response.json();
        setContextData(data);
      } catch (error) {
        console.error(error);
        setContextData({});
      }
    };

    const eraseUserProfileData = () => {
      setContextData({});
    };

    if (account) {
      loadUserProfileData();
    } else if (account === null) {
      eraseUserProfileData();
    }
  }, [account, username]);

  return (
    <UserProfileContext.Provider value={contextData}>
      {children}
    </UserProfileContext.Provider>
  );
};

export { UserProfileContext, UserProfileContextProvider };
