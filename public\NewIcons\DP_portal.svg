<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="80" height="80" viewBox="0 0 80 80">
  <defs>
    <filter id="Rectangle_39" x="0" y="0" width="80" height="80" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="Group_4895" data-name="Group 4895" transform="translate(-1691.268 -23.268)">
    <g transform="matrix(1, 0, 0, 1, 1691.27, 23.27)" filter="url(#Rectangle_39)">
      <rect id="Rectangle_39-2" data-name="Rectangle 39" width="62" height="62" rx="31" transform="translate(9 6)" fill="#fff"/>
    </g>
    <g id="Group_1980" data-name="Group 1980" transform="translate(1715.634 39.162)">
      <circle id="Ellipse_6" data-name="Ellipse 6" cx="9" cy="9" r="9" transform="translate(6.634 0.106)" fill="#c22042"/>
      <path id="Path_34" data-name="Path 34" d="M16.025,0A16.025,16.025,0,0,1,32.05,16.025c0,8.85-32.05,8.85-32.05,0A16.025,16.025,0,0,1,16.025,0Z" transform="translate(0 20.324)" fill="#c22042"/>
    </g>
  </g>
</svg>
