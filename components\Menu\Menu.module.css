.menuContainer {
  display: flex;
  justify-content: space-around;
  margin: 40px 0px;
  align-items: center;
}

.tabMenu {
  list-style: none;
  padding: 0;
  display: flex;
  width: 100%;
  gap: 2rem;

  @media (max-width: 768px) {
    flex-direction: column;
    padding: 0px 20px;
  }
}

.tab {
  padding: 0.6rem 0rem;
  cursor: pointer;
  color: #fff;
  display: flex;
  gap: 0.75rem;
  position: relative;
  font-weight: 500;
  transition: color 0.3s ease;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

@media (max-width: 768px) {
  .tab {
    padding: 0.6rem 0rem;
    cursor: pointer;
    color: #fff;
    display: flex;
    gap: 0.75rem;
    position: relative;
    font-weight: 500;
    transition: color 0.3s ease;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
  }
}

.tab::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  transition: width 0.4s ease;
}

.activeTab {
  border-bottom: 1px solid #fff;
}

.activeTab::after {
  width: 100%;
}


.inputSearchContainer {
  margin-right: 2rem;
}

.inputSearch {
  width: 350px;
  height: 40px;
  border-radius: 10px;
  border: 1px solid var(--primaryColor);
  padding: 0px 40px 0px 10px;
  font-size: 16px;
}

.widgetButton {
  background-color: #004890 !important;
  height: 40px !important;
}

/* Timeline Content Styling */
.notificationItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 20px 20px 0px;
  border-top: 1px solid #f9f7f5;

}

.notificationImage {
  flex-shrink: 0;
}

.notificationText {
  flex-grow: 1;
  margin-left: 10px;
}

.notificationHeader {
  color: var(--secondaryColor);
  font-weight: 500;
}

.notificationDate {
  color: var(--primaryColor);
  font-size: 14px;
  margin: 5px;
}

.notificationHeader,
.notificationDate {
  margin: 0;

}

.headerDate {
  font-size: 16px;
  font-weight: bold;
}

.contentHeader {
  font-size: 18px;
}

.contentDate {
  font-size: 12px;
  margin: 5px;
}

.contentDetails {
  font-size: 18px;
  align-self: center;
  font-style: italic;
  cursor: pointer;
}