import styles from "../../../WidgetDashboard/WidgetDashboard.module.css";
import Image from "next/image";
import Yes from '../../../../public/NewIcons/peoplesoft_Yes.svg'
import No from '../../../../public/NewIcons/peoplesoft_NO.svg'

export const ScheduleWidget = ({ gradeColor, data }) => {


  return (
    <div className={styles.gradesContainer}>
      <table className={styles.widgetTable}>
        <thead className={styles.dataTableHeader}>
          <tr>
            <th className={styles.tableHeader}>Class Name</th>
            <th className={styles.tableHeader}>Schedule</th>
            <th className={styles.tableHeader}>Location</th>
            <th className={styles.tableHeader}>Selected</th>
          </tr>
        </thead>
        <tbody>
          {data && data.map((item, index) => (
            <tr key={index}>
              <td className={styles.tableData}>{item.className}</td>
              <td className={styles.tableData}>{item.schedule}</td>
              <td className={styles.tableData}>{item.location}</td>
              <td className={styles.tableData}>{item.selected === "Y" ?
                (<span style={{ justifyContent: "center", display: "flex" }} className={`${styles.icon} ${styles.selectedIcon}`} title="Selected"><Image src={Yes} width={24} height={24} /></span>) :
                (<span style={{ justifyContent: "center", display: "flex" }} className={`${styles.icon} ${styles.notSelectedIcon}`} title="Not Selected"><Image src={No} width={24} height={24} /></span>)} </td>
            </tr>
          ))}
        </tbody>
      </table >
    </div >
  )
}