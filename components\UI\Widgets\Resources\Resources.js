import React from "react";
import { Accordion, AccordionTab } from 'primereact/accordion';

export const Resources = ({ }) => {
  return (
    <>
      <Accordion activeIndex={0}>
        <AccordionTab header="Academic Support">
          <ul>
            <li>
              <a href="https://www.csn.edu/academic-affairs" target="_blank" rel="noopener noreferrer">Academic Affairs</a>
            </li>
            <li>
              <a href="https://www.csn.edu/assessment-resources" target="_blank" rel="noopener noreferrer">Assessment Resources</a>
            </li>
            <li>
              <a href="https://www.csn.edu/resources-for-faculty-and-staff" target="_blank" rel="noopener noreferrer">CAS Resources For Faculty And Staff</a>
            </li>
            <li>
              <a href="https://www.csn.edu/csn-curriculum-and-scheduling" target="_blank" rel="noopener noreferrer">Curriculum and Scheduling</a>
            </li>
          </ul>
        </AccordionTab>
        <AccordionTab header="Faculty and Staff Support Services ">
          <ul>
            <li>
              <a href="https://www.csn.edu/csn-auxiliary-services" target="_blank" rel="noopener noreferrer">Auxiliary Services</a>
            </li>
            <li>
              <a href="https://www.csn.edu/cape" target="_blank" rel="noopener noreferrer">CAPE</a>
            </li>
            <li>
              <a href="https://www.csn.edu/cashier" target="_blank" rel="noopener noreferrer">Cashier</a>
            </li>
            <li>
              <a href="https://www.csn.edu/disability-resource-center" target="_blank" rel="noopener noreferrer">Disability Support Center</a>
            </li>
            <li>
              <a href="https://www.csn.edu/esl" target="_blank" rel="noopener noreferrer">English as a Second Language (ESL)</a>
            </li>
            <li>
              <a href="https://www.csn.edu/finance-division" target="_blank" rel="noopener noreferrer">Finance and Administration Division</a>
            </li>
            <li>
              <a href="https://www.csn.edu/assessment-resources" target="_blank" rel="noopener noreferrer">General Counsel (View Policies & Procedures)</a>
            </li>
          </ul>
        </AccordionTab>
        <AccordionTab header="Human Resources">
          <ul>
            <li>
              <a href="https://www.csn.edu/benefits-retirement" target="_blank" rel="noopener noreferrer">Benefits</a>
            </li>
            <li>
              <a href="https://www.csn.edu/employee-perks-and-discounts" target="_blank" rel="noopener noreferrer">Employee Perks and Discounts</a>
            </li>
            <li>
              <a href="https://www.csn.edu/employee-self-service" target="_blank" rel="noopener noreferrer">Employee Self Service (ESS)</a>
            </li>
            <li>
              <a href="https://www.csn.edu/_csnmedia/documents/employee-resources/Notary-List.pdf" target="_blank" rel="noopener noreferrer">Notary List</a>
            </li>
            <li>
              <a href="https://www.csn.edu/policies-and-procedures2" target="_blank" rel="noopener noreferrer">Policies & Procedures</a>
            </li>
            <li>
              <a href="https://www.csn.edu/shared-governance" target="_blank" rel="noopener noreferrer">Shared Governance</a>
            </li>
          </ul>
        </AccordionTab>
        <AccordionTab header="Faculty and Staff Technology Services">
          <ul>
            <li>
              <a href="https://www.csn.edu/ally" target="_blank" rel="noopener noreferrer">Ally</a>
            </li>
            <li>
              <a href="https://www.csn.edu/computerlabs" target="_blank" rel="noopener noreferrer">Computer Labs</a>
            </li>
            <li>
              <a href="https://www.csn.edu/email" target="_blank" rel="noopener noreferrer">CSN Email Information</a>
            </li>
            <li>
              <a href="https://www.csn.edu/faculty-resources" target="_blank" rel="noopener noreferrer">E-Learning Canvas LMS Assistance</a>
            </li>
            <li>
              <a href="https://www.csn.edu/faculty-faqs" target="_blank" rel="noopener noreferrer">E-Learning Canvas LMS FAQs</a>
            </li>
            <li>
              <a href="https://www.csn.edu/gocsn" target="_blank" rel="noopener noreferrer">Get to Know GoCSN FAQ</a>
            </li>
          </ul>
        </AccordionTab>
        <AccordionTab header="Faculty and Staff Campus Life">
          <ul>
            <li>
              <a href="https://www.csn.edu/administrative-faculty-assembly-afa" target="_blank" rel="noopener noreferrer">Administrative Faculty Assembly</a>
            </li>
            <li>
              <a href="https://www.csn.edu/forms/appreciation-luncheon" target="_blank" rel="noopener noreferrer">Administrative Faculty Luncheon</a>
            </li>
            <li>
              <a href="https://www.csn.edu/forms/administrative-faculty-month" target="_blank" rel="noopener noreferrer">Administrative Faculty of the Month</a>
            </li>
            <li>
              <a href="https://www.csn.edu/classified-council" target="_blank" rel="noopener noreferrer">Classified Council</a>
            </li>
            <li>
              <a href="https://www.csn.edu/classified-council#Classified" target="_blank" rel="noopener noreferrer">Classified Employee of the Month</a>
            </li>
            <li>
              <a href="https://www.csn.edu/collective-bargaining" target="_blank" rel="noopener noreferrer">Collective Bargaining</a>
            </li>
          </ul>
        </AccordionTab>
        <AccordionTab header="Most Used Links">
          <ul>
            <li>
              <a href="https://www.csn.edu/csn-auxiliary-services" target="_blank" rel="noopener noreferrer">Worday Evaluation</a>
            </li>
            <li>
              <a href="https://www.csn.edu/cape" target="_blank" rel="noopener noreferrer">Academic calendar and holidays</a>
            </li>
            <li>
              <a href="https://www.csn.edu/cashier" target="_blank" rel="noopener noreferrer">CSN events calendar</a>
            </li>
            <li>
              <a href="https://www.csn.edu/esl" target="_blank" rel="noopener noreferrer">CSN news feed</a>
            </li>
          </ul>
        </AccordionTab>
      </Accordion>
    </>
  )
}