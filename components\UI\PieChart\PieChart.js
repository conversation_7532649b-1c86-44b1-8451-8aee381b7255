import React, { useState, useEffect, useRef } from 'react';
import { Chart, registerables } from 'chart.js';

export default function PieChart({ data, id }) {
  const canvasRef = useRef(null);

  useEffect(() => {
    Chart.register(...registerables);

    if (canvasRef.current?.chartInstance) {
      canvasRef.current.chartInstance.destroy();
    }

    const ctx = canvasRef.current.getContext('2d');
    const newChart = new Chart(ctx, {
      type: 'pie',
      data: data,
      options: {
        responsive: true,
      },
    });

    canvasRef.current.chartInstance = newChart;

    return () => {
      newChart.destroy();
    };
  }, [data, id]);

  return (
    <div>
        <canvas id={id} ref={canvasRef} />
    </div>
  );
};