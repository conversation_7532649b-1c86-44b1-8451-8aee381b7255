import React, { useState, useEffect } from "react";
import styles from "../../../WidgetDashboard/WidgetDashboard.module.css";
import { formBuilderApiRequest } from "../../../../utilities/Msal/msalConfig";
import { useAccount, useMsal, useMsalAuthentication } from "@azure/msal-react";
import { useApi } from "../../../../hooks/useApi";
import useUtilityFunctions from "../../../../hooks/useUtilityFunctions";
import { InteractionType } from "@azure/msal-browser";
import { useDashboard } from "../../../../hooks/useDashboard";
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  Tooltip,
  Legend,
  CartesianGrid,
  ResponsiveContainer,
  LabelList,
} from "recharts";

const api = process.env.NEXT_PUBLIC_FORM_BUILDER_API_DEMO;
export const EmployeeOnboarding = ({userName}) => {

  const { accounts } = useMsal();
  const { loading, callApi } = useApi();
  const account = useAccount(accounts[0] ?? {});
  const { createDashboardDate } = useUtilityFunctions();
  const { acquireToken } = useMsalAuthentication(
    InteractionType.Silent,
    formBuilderApiRequest
  );

const dataForJohn = [
  { name: 'Facilities Provisioning', Rejected: 0, Inprogress: 2, Approved: 3 },
  { name: 'Security', Rejected: 1, Inprogress: 0, Approved: 4 },
  { name: 'IT Provisioning', Rejected: 0, Inprogress: 3, Approved: 2 },
  { name: 'TeleComm Provisioning', Rejected: 1, Inprogress: 2, Approved: 2 },
  { name: 'IT Account', Rejected: 1, Inprogress: 3, Approved: 1 },
];

const dataForBarkly = [
  { name: 'Facilities Provisioning', Rejected: 1, Inprogress: 1, Approved: 4 },
  { name: 'Security', Rejected: 0, Inprogress: 3, Approved: 3 },
  { name: 'IT Provisioning', Rejected: 2, Inprogress: 2, Approved: 2 },
  { name: 'TeleComm Provisioning', Rejected: 1, Inprogress: 2, Approved: 3 },
  { name: 'IT Account', Rejected: 2, Inprogress: 3, Approved: 1 },
];

// Determine the data based on the username
const defaultData = userName?.toLowerCase() === "john keating" ? dataForJohn : dataForBarkly;
const ytdCount = userName?.toLowerCase() === "john keating" ? 5 : 6;
  const initialLazyParams = {
    page: 0,
    rows: 30,
    sortField: "initiatedDate",
    sortOrder: -1,
    solutionId: 3,
  };

  const {
    rows,
    setRows,
    totalCount,
    setTotalCount,
    lazyParams,
    globalFilter,
    lazyParamsToQueryString,
    onSort,
    onPage,
    onFilter,
    onGlobalFilterChange,
  } = useDashboard({ initialLazyParams });

  const [processedData, setProcessedData] = useState(null);

  useEffect(() => {
    const loadAllData = async () => {
      if (account) {
        try {
          const { accessToken } = await acquireToken();
          const queryString = lazyParamsToQueryString(lazyParams);

          // Define all base URLs
          const urlBaseMapper = {
            InProgress: `FormSubmission/filter/email/status/2/finalized/false`,
            Approved: `FormSubmission/filter/email/status/3/finalized/true`,
            Rejected: `FormSubmission/filter/email/status/4/finalized/true`,
          };

          const apiCalls = Object.entries(urlBaseMapper).map(
            async ([tabName, baseUrl]) => {
              const userFormApprovalUrl = `${api}${baseUrl}${queryString}`;

              const formSubmissionParams = {
                method: "GET",
                url: userFormApprovalUrl,
                headers: {
                  Accept: "*/*",
                  Authorization: `Bearer ${accessToken}`,
                  "next-action": "userFormApproval",
                },
              };

              const result = await callApi(formSubmissionParams);
              return { tabName, data: result?.data };
            }
          );

          const results = await Promise.all(apiCalls);
          const processedData = processData(results);
          // setProcessedData(processedData);
          setProcessedData(defaultData);
        } catch (error) {
          console.error("Error loading data:", error);
        }
      }
    };

    loadAllData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [lazyParams, acquireToken, account]);

  const processData = (data) => {
    const resultMap = new Map();

    // Process incoming data
    data.forEach((record) => {
      const formSubmissions = record?.data?.formSubmissions; // Assuming it's an array

      if (!Array.isArray(formSubmissions)) {
        console.warn("formSubmissions is not an array:", record);
        return; // Skip if not an array
      }

      formSubmissions.forEach((submission) => {
        const formName = submission?.formDefinition?.name;
        const status = submission?.statusName;

        if (!formName || !status) {
          console.warn(
            "Missing formDefinition name or statusName:",
            submission
          );
          return; // Skip records with missing data
        }

        if (!resultMap.has(formName)) {
          resultMap.set(formName, {
            name: formName,
            InProgress: 0,
            Rejected: 0,
            Approved: 0,
          });
        }

        const formEntry = resultMap.get(formName);
        formEntry[status] = (formEntry[status] || 0) + 1;
      });
    });

    // Convert Map to an array
    const processedData = Array.from(resultMap.values());

    // Merge with default data
    const mergedData = defaultData.map((defaultItem) => {
      const existingItem = processedData.find(
        (item) => item.name === defaultItem.name
      );
      return existingItem || defaultItem;
    });

    return mergedData;
  };

  const CustomTooltip = ({ active, payload }) => {
    if (active && payload && payload.length) {
      return (
        <div
          style={{
            backgroundColor: "#ffffff",
            border: "1px solid #cccccc",
            borderRadius: "5px",
            padding: "10px",
          }}
        >
          <p
            style={{ color: "#004990", fontSize: "14px" }}
          >{`Name: ${payload[0].payload.name}`}</p>
          <p
            style={{ color: "#004990", fontSize: "14px" }}
          >{`Rejected: ${payload[0].payload.Rejected}`}</p>
          <p
            style={{ color: "#004990", fontSize: "14px" }}
          >{`In Progress: ${payload[0].payload.Inprogress}`}</p>
          <p
            style={{ color: "#004990", fontSize: "14px" }}
          >{`Approved: ${payload[0].payload.Approved}`}</p>
        </div>
      );
    }

    return null;
  };

  return (
    <div>
      <h2 style={{ fontSize: "18px", fontWeight: "700" }}>Employees joining my team - YTD: {ytdCount}</h2>
      <h2 style={{ fontSize: "18px", fontWeight: "600" }}>Requests per Line Item</h2>
      <ResponsiveContainer width="108%" height={450}>
        <BarChart
          data={processedData}
          layout="vertical"
          margin={{ top: 30, right: 30, left: -60 }}
          barCategoryGap="25%"
          style={{ margin: "0 auto" }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis type="number" />
          <YAxis
            dataKey="name"
            type="category"
            width={200}
            tick={{
              fontSize: 14,
              fill: "#004890",
              textAnchor: "end",
              dx: 0,
              left: "-50",
              style: { lineHeight: "1.5" },
            }}
          />

          <Tooltip content={<CustomTooltip />} />
          <Legend
            verticalAlign="bottom"
            iconType="circle"
            wrapperStyle={{
              paddingTop: "0px",
              lineHeight: "24px",
              width: "90%",
              left: "0",
            }}
            formatter={(value) => {
              switch (value) {
                case "Inprogress":
                  return "In Progress";
                default:
                  return value;
              }
            }}
          />
          <Bar key="Rejected" dataKey="Rejected" stackId="a" fill="#f96158" />
          <Bar
            key="In Progress"
            dataKey="Inprogress"
            stackId="a"
            fill="#f9e18e"
          />
          <Bar key="Approved" dataKey="Approved" stackId="a" fill="#6ef06e" />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};
