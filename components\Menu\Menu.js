import { InputText } from "primereact/inputtext";
import styles from "./Menu.module.css";
import { Button } from "primereact/button";
import { OverlayPanel } from "primereact/overlaypanel";
import Image from "next/image";
import arrow from "../../public/SSOImages/right-arrow.svg"


export const MenuContainer = ({ children }) => {
  return <div className={styles.menuContainer}>{children}</div>;
};

export const TabList = ({ children }) => {
  return <ul className={styles.tabMenu}>{children}</ul>;
};

export const Tab = ({
  value,
  activeTab,
  label,
  iconClassName,
  iconActive,
  iconInactive,
  setActiveTabIndex,
}) => (
  <li key={value} className={`${styles.tab} ${activeTab === value ? styles.activeTab : ''}`} onClick={() => setActiveTabIndex(value)}>
    {/* <li key={value} className="cursor-pointer text-color-white flex gap-3 py-2 px-4 font-medium relative transition-colors duration-300 ease-in-out" onClick={() => setActiveTabIndex(value)}> */}
    {iconActive && iconInactive ? (
      activeTab === value ? (
        iconActive
      ) : (
        iconInactive
      )
    ) : (
      <i className={iconClassName}></i>
    )}
    {label}
  </li>
);

export const InputSearch = ({ value, onChange }) => {
  return (
    // <span className={`p-input-icon-right ${styles.inputSearchContainer}`}>
    <div className="p-input-icon-right flex justify-between items-center">
      <i className="pi pi-search font-bold headerSearchIcon" style={{ borderLeft: "1px solid #c12042", padding: "2px 10px", fontSize: "1rem", lineHeight: "0.8rem", position: "absolute", left: "86%" }} />
      <InputText
        className="w-40 md:w-80 h-8 md:h-11 pl-3 font-open-sans rounded-[0px] text-color-dark-red text-base border text-sm md:text-base headerInputSearch"
        placeholder="Search"
        value={value}
        onChange={(e) => onChange(e.target.value)}
      />
    </div>
  );
};

export const OverlayButton = ({
  panelRef,
  label,
  icon,
  children,
  setIsBlur,
  width = "500px",
}) => {

  return (
    <>
      <Button
        className=" appointmentButton bg-color-dark-red px-7 md:px-8 h-11 rounded-[0px] text-white font-semibold"
        label={label}
        icon={icon}
        onClick={(e) => {
          panelRef.current.toggle(e)
          // setIsBlur(prev=> !prev)
        }}
      />
      <OverlayPanel ref={panelRef}
        onShow={() => { setIsBlur(true) }} onHide={() => { setIsBlur(false) }} style={{ width: width, zIndex: "999" }}>
        {children}
      </OverlayPanel>
    </>

  );
};

export const TimelineContent = ({ item }) => {
  return (
    <div className={styles.notificationContent}>
      <div className={styles.headerDate}>
        <div className={styles.contentHeader}>{item.content.header}</div>
        <p className={styles.contentDate}>{item.content.dateTime}</p>
      </div>
      <span className={styles.contentDetails}>
        {item.content.details}{" "}
        <i className={`${styles.arrowIcon} pi pi-arrow-right`}></i>
      </span>
    </div>
  );
};

export const TimelineMarker = ({ item }) => {
  return <Image src={item.image} alt="Event Image" width={30} height={30} />;
};

export const NotificationItem = ({ item }) => {
  return (
    <div className={styles.notificationItem}>
      <div className={styles.notificationImage}>
        <Image
          src={item.image}
          alt="Notification"
          width={item.imageWidth}
          height={item.imageHeight}
        />
      </div>
      <div className={styles.notificationText}>
        <div className={styles.notificationHeader}>{item.content.header}</div>
        <div className={styles.notificationDate}>{item.content.dateTime}</div>
      </div>
      <div className={styles.notificationArrow}>
        <i><Image src={arrow} /></i>
      </div>
    </div>
  );
};
