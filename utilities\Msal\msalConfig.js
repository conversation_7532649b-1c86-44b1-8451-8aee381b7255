import { LogLevel } from "@azure/msal-common";

export const msalConfig = {
    auth: {
        clientId: '0da22cba-df03-42f1-979d-60d9681c493a',
        authority: 'https://login.microsoftonline.com/04aee3cb-4ce8-410a-8596-5e52728fc79a',
        redirectUri: "/blank", 
        postLogoutRedirectUri: "/" 
    },
    cache: {
        cacheLocation: "localStorage",
        storeAuthStateInCookie: true
    },
    system: {	
        loggerOptions: {	
            loggerCallback: (level, message, containsPii) => {	
                if (containsPii) {		
                    return;		
                }		
                switch (level) {
                    case LogLevel.Error:
                        console.error(message);
                        return;
                    case LogLevel.Info:
                        console.info(message);
                        return;
                    case LogLevel.Verbose:
                        console.debug(message);
                        return;
                    case LogLevel.Warning:
                        console.warn(message);
                        return;
                    default:
                        return;
                }	
            }	
        }	
    }
}   

export const loginRequest = {
    scopes: ['User.Read', 'profile', 'openid', 'email']
}

export const dataverseApiRequest = {
    scopes: ['https://org4e09c964.crm.dynamics.com/user_impersonation']
}

export const formBuilderApiRequest = {
    scopes: ['api://2e825532-5a1a-401b-93c0-db10a48cdf75/All']
}

export const graphConfig = {
    graphMeEndpoint: "https://graph.microsoft.com/v1.0/me"
}


