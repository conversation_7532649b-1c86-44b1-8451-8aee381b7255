.tile {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;

  cursor: pointer;
  border-radius: 20px;
  justify-self: center;

  @media(max-width:768px) {
    width: 360px !important;
  }
}

.tileTitle {
  font-weight: bold;
  width: 100px;
  color: #002138;
  font-size: 12px;
  max-width: 66px;
}

.tileNumber {
  font-size: 20px;
  font-weight: 700;
  border-radius: 10px;
  color: white;
  width: auto;
  min-width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.tileNumber_singleDigit {
  padding: 0.25rem 0.25rem;
  font-weight: 700;
}

.tileBody {
  width: 100%;
  height: 156px;
  flex: 1;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 1rem 6px;
}

.tileTextContainer {
  margin-left: 8px;
}

.tileIconContainer {
  display: flex;
}

.tileIcon {
  font-size: 1rem;
}

.green {
  color: green;
}

.red {
  color: red;
}

.tileFooter {
  width: 100%;
  display: flex;
  font-weight: 600;
  margin-left: 0.5rem;
  align-items: flex-end;
  color: var(--primary-bg-blackPearl)
}

.tileFooterText {
  font-size: 14px;
  color: var(--primary-bg-blackPearl)
}

.rowContainer {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  padding: 0 2rem 2rem;
  background-color: white;
  border-radius: 10px;
  margin: 20px 0;
}

@media screen and (max-width: 1280px) and (max-height: 720px) {
  .rowContainer {
    padding: 0 1rem 2rem;

  }
}

.rowContainerSubmissions {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  margin-bottom: 1rem;
  margin-top: 1.5rem;

}

.tileActive {
  border-radius: 20px;
  justify-self: center;
}

.inProgressBackgroundColor {
  background-color: var(--secondary-bg-goldenPoppy-20);
}

.inProgressActiveStyle {
  border: 3px solid var(--secondary-bg-goldenPoppy);
}

.inProgressNumberBackgroundColor {
  background-color: var(--secondary-bg-goldenPoppy);
}

.approvedBackgroundColor {
  background-color: var(--tertiary-bg-limeGreen-20);
}

.approvedNumberBackgroundColor {
  background-color: var(--tertiary-bg-limeGreen);
}

.approvedActiveColor {
  border: 3px solid var(--tertiary-bg-limeGreen);
}

.rejectedBackgroundColor {
  background-color: #FFE8E5;
}

.rejectedNumberBackgroundColor {
  background-color: #FF2300;
}

.rejectedActiveColor {
  border: 3px solid #FF2300;
}

.pendingBackgroundColor {
  background-color: var(--secondary-bg-deepBlueSky-20);
}

.pendingNumberBackgroundColor {
  background-color: var(--secondary-bg-deepBlueSky);
}

.pendingActiveColor {
  border: 3px solid var(--secondary-bg-deepBlueSky);
}

.finalizedNumberBackgroundColor {
  background-color: #00B9FF;
}

.finalizedActiveColor {
  border: 3px solid #00B9FF;
}

.finalizedBackgroundColor {
  background-color: #CCF1FF;
}

.tileInactive {
  border-radius: 20px;
  justify-self: center;
}

.totalFinalizedNumberBackgroundColor {
  background-color: var(--primary-bg-darkCerulean);
}

.totalFinalizedActiveColor {
  border: 3px solid var(--primary-bg-darkCerulean);
}

.totalFinalizedBackgroundColor {
  background-color: var(--primary-bg-darkCerulean-20);
}