/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable SWC minification for faster builds
  swcMinify: true,

  // Enable compiler optimizations
  compiler: {
    // Remove console.log in production
    removeConsole: process.env.NODE_ENV === "production",
  },

  // Optimize images
  images: {
    formats: ["image/webp", "image/avif"],
    minimumCacheTTL: 60,
  },

  // Enable experimental features for better performance
  experimental: {
    // Enable SWC plugins
    swcPlugins: [],

    // Optimize CSS
    optimizeCss: true,

    // Enable turbo mode for faster builds
    turbo: {
      rules: {
        "*.svg": {
          loaders: ["@svgr/webpack"],
          as: "*.js",
        },
      },
    },

    serverActions: {
      allowedOrigins: [
        "https://localhost:3000",
        "https://localhost",
        "https://**************",
        "https://ec2-18-222-175-215.us-east-2.compute.amazonaws.com",
      ],
      allowedForwardedHosts: [
        "https://localhost:3000",
        "https://localhost",
        "https://**************",
        "https://ec2-18-222-175-215.us-east-2.compute.amazonaws.com",
      ],
    },
  },

  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // Optimize webpack configuration
    if (!isServer) {
      config.resolve.fallback = {
        fs: false,
        net: false,
        tls: false,
        dns: false,
      };
    }

    // Enable webpack caching for faster rebuilds
    config.cache = {
      type: "filesystem",
      buildDependencies: {
        config: [__filename],
      },
    };

    // Optimize chunk splitting
    config.optimization = {
      ...config.optimization,
      splitChunks: {
        chunks: "all",
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: "vendors",
            chunks: "all",
          },
          charts: {
            test: /[\\/]node_modules[\\/](chart\.js|react-chartjs-2|recharts|@mui\/x-charts)[\\/]/,
            name: "charts",
            chunks: "all",
            priority: 10,
          },
          primereact: {
            test: /[\\/]node_modules[\\/]primereact[\\/]/,
            name: "primereact",
            chunks: "all",
            priority: 10,
          },
        },
      },
    };

    // Add bundle analyzer in development
    if (process.env.ANALYZE === "true") {
      const BundleAnalyzerPlugin =
        require("webpack-bundle-analyzer").BundleAnalyzerPlugin;
      config.plugins.push(new BundleAnalyzerPlugin());
    }

    return config;
  },

  // Enable output file tracing for smaller deployments
  output: "standalone",

  // Optimize build performance
  poweredByHeader: false,

  // Enable compression
  compress: true,
};

module.exports = nextConfig;
