/** @type {import('next').NextConfig} */
const nextConfig = {
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        fs: false,
        net: false,
        tls: false,
        dns: false,
      };
    }
    return config;
  },
  experimental: {
    serverActions: {
      allowedOrigins: [
        "https://localhost:3000",
        "https://localhost",
        "https://**************",
        "https://ec2-18-222-175-215.us-east-2.compute.amazonaws.com",
      ],
      allowedForwardedHosts: [
        "https://localhost:3000",
        "https://localhost",
        "https://**************",
        "https://ec2-18-222-175-215.us-east-2.compute.amazonaws.com",
      ],
    },
  },
};

module.exports = nextConfig;
