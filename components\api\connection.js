import { Pool } from "pg";

const pool = new Pool({
  user: "postgres",
  password: "C<PERSON>Jack!fucan",
  host: "ed-portal.cxdfnijikvqs.us-east-2.rds.amazonaws.com",
  port: 5432,
  database: "EDPortal",
  ssl: { rejectUnauthorized: false },
});

export async function query(text, params = []) {
  const client = await pool.connect();
  try {
    const result = await client.query(text, params);
    return result.rows;
  } catch (error) {
    console.error("Query error:", error.message, error.stack);
    throw error; // Re- throw error for proper handling upstream
  } finally {
    client.release(); //Ensure  client is released back to the pool
  }
}
