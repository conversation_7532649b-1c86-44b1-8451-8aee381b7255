import { useEffect, useState } from "react";
import { getAccessTokenForScope } from "../utilities/Msal/GetAccessTokenForScope";
import { formBuilderApiRequest } from "../utilities/Msal/msalConfig";

const api = process.env.NEXT_PUBLIC_FORM_BUILDER_API_DEMO;

export const defaultApprovalTileValues = {
  currentWeekInProgress: 0,
  currentWeekApproved: 0,
  currentWeekRejected: 0,
  currentWeekFinalized: 0,
  pastWeekInProgress: 0,
  pastWeekApproved: 0,
  pastWeekRejected: 0,
  pastWeekFinalized: 0,
  dailyProcessed: [0, 0, 0, 0, 0, 0, 0],
  dailyAssigned: [0, 0, 0, 0, 0, 0, 0],
};

export const useApprovalTileValues = ({
  solutionId = 1,
  fetchUserTileValues = true,
  fetchAllTileValues = true,
}) => {
  const [myApprovalChartValues, setMyApprovalChartValues] = useState(
    defaultApprovalTileValues
  );
  const [allApprovalChartValues, setAllApprovalChartValues] = useState(
    defaultApprovalTileValues
  );

  useEffect(() => {
    const fetchMyApprovalValues = async () => {
      try {
        const accessToken = await getAccessTokenForScope(formBuilderApiRequest);
        const response = await fetch(
          `${api}FormSubmission/MyApprovalsDashboard?solutionId=${solutionId}`,
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
              "next-action": "MyApprovalsDashboard",
            },
          }
        );

        const json = await response.json();
        setMyApprovalChartValues(json);
      } catch (error) {
        console.error("Failed to fetch my approval values:", error);
      }
    };

    const fetchAllApprovalChartValues = async () => {
      try {
        const accessToken = await getAccessTokenForScope(formBuilderApiRequest);

        const response = await fetch(
          `${api}FormSubmission/AdminDashboard?solutionId=${solutionId}`,
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
              "next-action": "AdminDashboard",
            },
          }
        );

        const json = await response.json();
        setAllApprovalChartValues(json);
      } catch (error) {
        console.error("Failed to fetch status counts:", error);
      }
    };

    if (fetchUserTileValues) {
      fetchMyApprovalValues();
    }
    if (fetchAllTileValues) {
      fetchAllApprovalChartValues();
    }
  }, [solutionId, fetchUserTileValues, fetchAllTileValues]);

  return { myApprovalChartValues, allApprovalChartValues };
};
