import { Tile, TileContainer } from "../../Tile/Tile" 


const FormTiles = ({ userTileValues, handleTileClick, selectedTile }) => {
  return (
    <TileContainer>
      <Tile 
        title="Pending" 
        colorOption="Pending" 
        number={userTileValues.draftsCount + userTileValues.recalledCount}  
        handleClick={() => handleTileClick("Pending")}
        isActive={selectedTile === "Pending"}
      />
      <Tile 
        title="In Progress" 
        colorOption="InProgress" 
        number={userTileValues.inProgressCount + userTileValues.onHoldCount} 
        handleClick={() => handleTileClick("InProgress")} 
        isActive={selectedTile === "InProgress"}
      />
      <Tile 
        title="Finalized" 
        colorOption="TotalFinalized" 
        number={userTileValues.approvedCount + userTileValues.rejectedCount + userTileValues.cancelledCount} 
        handleClick={() => handleTileClick("Finalized")} 
        isActive={selectedTile === "Finalized"}
      />
    </TileContainer>
  )
}