NEXT_PUBLIC_FORM_BUILDER_API=https://formbuilderstudio.azurewebsites.net/api/
NEXT_PUBLIC_DATAVERSE_API=https://org4e09c964.crm.dynamics.com/api/data/v9.2/
NEXT_PUBLIC_FORM_BUILDER_API_QA=https://formbuilderstudio-qa.azurewebsites.net/api/
NEXT_PUBLIC_FORM_BUILDER_API_DEMO=https://formbuilderstudio-demo.azurewebsites.net/api/
# NEXT_PUBLIC_DMSBASEAPI=http://localhost:8101/docs-web/api/
NEXT_PUBLIC_DMSBASEAPI=https://altinworks.com:8443/PedaDocs/api/
# NEXT_PUBLIC_DMSBASEAPI=https://www.friendlylegal.shop/api/
# NEXT_PUBLIC_DMSBASEAPI=http://acaddoc.westus.cloudapp.azure.com:8080/docs-web/api/
NEXT_PUBLIC_DMS_ORGNAME="College of Southern Nevada"
VERCEL_PREVIEW_FEEDBACK_ENABLED=0
# LLM_SERVER_API=http://localhost:8000/
NEXT_PUBLIC_LLM_SERVER_API=https://api.ordact.com/
# Property
NEXT_PUBLIC_PROPERTY_NAME="SAU"


# Encryption
ALGORITHM=aes-256-gcm
SECRET=3058cb058b8e1569bae2da78619fb48a
# Property
NEXT_PUBLIC_PROPERTY_NAME="UTS"

#name
NEXT_PUBLIC_UNIVERSITY_NAME="The University of Texas System"

# # Property
# NEXT_PUBLIC_PROPERTY_NAME="SAU"

# #name
# NEXT_PUBLIC_UNIVERSITY_NAME="Southern Arizona University"