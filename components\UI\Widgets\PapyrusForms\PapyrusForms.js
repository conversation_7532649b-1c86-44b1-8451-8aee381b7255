import React, { useState, useEffect } from "react";
import { Tile, TileContainer } from "../../Tile/Tile";
import { Tab, TabContainer } from "../../Tabs/Tabs";
import { InteractionType } from "@azure/msal-browser";
import { formBuilderApiRequest } from "../../../../utilities/Msal/msalConfig";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
// Icons
import Recall from "../../../../public/Icons/Recall.svg";
import Draft from "../../../../public/Icons/Draft.svg";
import InReview from "../../../../public/Icons/Inreview.svg";
import Hold from "../../../../public/Icons/Hold.svg";
import Approved from "../../../../public/Icons/Approved.svg";
import Rejected from "../../../../public/Icons/Rejected.svg";
import Cancelled from "../../../../public/Icons/Cancelled.svg";
// Hook
import { useSubmissionTileValues } from "../../../../hooks/useSubmissionTileValues";
import { useAccount, useMsal, useMsalAuthentication } from "@azure/msal-react";
import { useDashboard } from "../../../../hooks/useDashboard";
import { SubmissionStages } from "../../../../utilities/Msal/constants";
import { useApi } from "../../../../hooks/useApi";
import useUtilityFunctions from "../../../../hooks/useUtilityFunctions";

const api = process.env.NEXT_PUBLIC_FORM_BUILDER_API_DEMO;

const getStatusFromTab = (tab) => {
  switch (tab) {
    case "Drafts":
      return SubmissionStages.Draft;
    case "Recalled":
      return SubmissionStages.Recall;
    case "In Review":
      return SubmissionStages.Inprogress;
    case "On Hold":
      return SubmissionStages.OnHold;
    case "Approved":
      return SubmissionStages.Approved;
    case "Rejected":
      return SubmissionStages.Rejected;
    case "Cancelled":
      return SubmissionStages.Cancelled;
    default:
      return 3;
  }
};

const tabMapper = {
  Pending: ["Drafts", "Recalled"],
  InProgress: ["In Review", "On Hold"],
  Finalized: ["Approved", "Rejected", "Cancelled"],
};

const iconMapper = {
  Drafts: Draft,
  Recalled: Recall,
  "In Review": InReview,
  "On Hold": Hold,
  Approved: Approved,
  Rejected: Rejected,
  Cancelled: Cancelled,
};

const handleRowClick = (value) => {
  const baseUrl =
    "https://demo.papyrrus.com/form-builder-studio/view/";
  let url = "";

  if (value?.status === 11) {
    // 11 is the status code for Drafts
    url = `${baseUrl}${value.formDefinition.id}/drafts/${value.id}`;
  } else {
    url = `${baseUrl}${value.formDefinition.id}/mySubmissions/${value.id}`;
  }

  window.open(url, "_blank");
};

export const PapyrusForms = ({ solutionId = 1 }) => {
  const { accounts } = useMsal();
  const { loading, callApi } = useApi();
  const account = useAccount(accounts[0] ?? {});
  const { createDashboardDate } = useUtilityFunctions();
  const { acquireToken } = useMsalAuthentication(
    InteractionType.Silent,
    formBuilderApiRequest
  );
  const headerStyle = { fontWeight: "600", fontSize: "15.5px", color: "#000" };
  const initialLazyParams = {
    first: 0,
    page: 0,
    rows: 10,
    sortField: "initiatedDate",
    sortOrder: -1,
    filters: {
      global: {
        value: "",
        matchMode: "contains",
      },
    },
  };

  const {
    rows,
    setRows,
    totalCount,
    setTotalCount,
    lazyParams,
    globalFilter,
    lazyParamsToQueryString,
    onSort,
    onPage,
    onFilter,
    onGlobalFilterChange,
  } = useDashboard({ initialLazyParams });

  const submittedAtBodyTemplate = (formSubmission) => {
    const { submittedAtUtc } = formSubmission;
    return createDashboardDate(submittedAtUtc); // Reactor function to convert date to readable format
  };

  const lastUpdatedBodyTemplate = (formSubmission) => {
    const { lastUpdatedAtUtc } = formSubmission;
    return createDashboardDate(lastUpdatedAtUtc); // Reactor function to convert date to readable format
  };

  const { userTileValues } = useSubmissionTileValues({ solutionId });

  const [selectedTile, setSelectedTile] = useState("Pending");
  const [selectedTab, setSelectedTab] = useState(tabMapper["Pending"][0]);

  const handleTileClick = (tile) => {
    setSelectedTile(tile);
    setSelectedTab(tabMapper[tile][0]);
  };

  const handleTabClick = (tab) => {
    setSelectedTab(tab);
  };

  useEffect(() => {
    const getFinalizedStatus = () => {
      return (
        selectedTab === "Approved" ||
        selectedTab === "Rejected" ||
        selectedTab === "Cancelled"
      );
    };

    const loadLazyData = async () => {
      // Rename this function to something more descriptive
      if (account) {
        const { accessToken } = await acquireToken();
        const queryString = lazyParamsToQueryString(lazyParams);
        const status = getStatusFromTab(selectedTab);
        const userFormSubmissionsUrl = `${api}FormSubmission/filter/email/status/${status}/finalized/${getFinalizedStatus()}${queryString}&solutionId=${solutionId}`;

        const formSubmissionParams = {
          method: "GET",
          url: userFormSubmissionsUrl,
          headers: {
            Accept: "*/*",
            Authorization: `Bearer ${accessToken}`,
            "next-action": "userFormSubmissions",
          },
          data: {
            query: queryString,
          },
        };
        const result = await callApi(formSubmissionParams);
        setRows(result?.data?.formSubmissions);
        setTotalCount(result?.data?.count);
      }
    };

    loadLazyData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [lazyParams, acquireToken, account, selectedTab]);

  //For the TileContainer and Tile, create its own component and pass the userTileValues, handleTileClick, and selectedTile as props
  return (
    <>
      <TileContainer>
        <Tile
          title="Pending"
          colorOption="Pending"
          number={userTileValues.draftsCount + userTileValues.recalledCount}
          handleClick={() => handleTileClick("Pending")}
          isActive={selectedTile === "Pending"}
        />
        <Tile
          title="In Progress"
          colorOption="InProgress"
          number={userTileValues.inProgressCount + userTileValues.onHoldCount}
          handleClick={() => handleTileClick("InProgress")}
          isActive={selectedTile === "InProgress"}
        />
        <Tile
          title="Finalized"
          colorOption="TotalFinalized"
          number={
            userTileValues.approvedCount +
            userTileValues.rejectedCount +
            userTileValues.cancelledCount
          }
          handleClick={() => handleTileClick("Finalized")}
          isActive={selectedTile === "Finalized"}
        />
      </TileContainer>
      <TabContainer>
        {tabMapper[selectedTile].map((tab) => (
          <Tab
            key={tab}
            title={tab}
            isActive={selectedTab === tab}
            handleClick={() => handleTabClick(tab)}
            display={true}
            icon={iconMapper[tab]}
          />
        ))}
      </TabContainer>
      <DataTable
        value={rows}
        lazy
        columnResizeMode="expand"
        dataKey="id"
        paginator
        first={lazyParams.first}
        rows={lazyParams.rows}
        onSort={onSort}
        onPage={onPage}
        onFilter={onFilter}
        sortField={lazyParams.sortField}
        sortOrder={lazyParams.sortOrder}
        totalRecords={totalCount}
        filters={lazyParams.filters}
        loading={loading}
        globalFilterFields={[]}
        selectionMode="single"
        onSelectionChange={(e) => handleRowClick(e.value)}
      >
        <Column
          className="dashboardTitle"
          field="formSubmissionId"
          header="Form ID"
          sortable
          headerStyle={{ ...headerStyle, width: "7%" }}
        />
        <Column
          className="dashboardTitle"
          field="formDefinition.name"
          header="Form Title"
          sortable
          headerStyle={{ ...headerStyle, width: "10%" }}
        />
        <Column
          className="dashboardTitle"
          field="submittedAtUtc"
          header="Date Submitted"
          body={submittedAtBodyTemplate}
          sortable
          headerStyle={{ ...headerStyle, width: "11%" }}
        />
        <Column
          className="dashboardTitle"
          field="lastUpdatedAtUtc"
          header="Last Updated"
          body={lastUpdatedBodyTemplate}
          sortable
          headerStyle={{ ...headerStyle, width: "10%" }}
        />
      </DataTable>
    </>
  );
};
