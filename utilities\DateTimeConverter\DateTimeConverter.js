 export function formatDateForHeader(dateString) {
  const date = new Date(dateString)
  const options = { weekday: 'long', month: 'long', day: 'numeric' }
  let formattedDate = date.toLocaleDateString('en-US', options)
  const day = date.getDate()
  let suffix = "th"

  if (day % 10 === 1 && day !== 11) suffix = "st"
  else if (day % 10 === 2 && day !== 12) suffix = "nd"
  else if (day % 10 === 3 && day !== 13) suffix = "rd"

  return formattedDate.replace(new RegExp(`${day}`), `${day}${suffix}`)
}

 export function formatTime(timeString) {
  const time = new Date(timeString)
  return time.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true })
}

export const formatDate = (isoDateString) => {
  const date = new Date(isoDateString)
  const options = { month: 'long', day: 'numeric', year: 'numeric' }
  return date.toLocaleDateString('en-US', options)
}

export function formatDateTime(dateString) {
  const date = new Date(dateString);
  
  // Extract the date parts
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const year = String(date.getFullYear()).slice(2);
  
  // Extract the time parts
  let hours = date.getHours();
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const ampm = hours >= 12 ? 'pm' : 'am';
  hours = hours % 12;
  hours = hours ? hours : 12; // the hour '0' should be '12'
  
  // Format the date and time parts
  const formattedDate = `${month}/${day}/${year}`;
  const formattedTime = `${String(hours).padStart(2, '0')}:${minutes} ${ampm}`;
  
  // Combine date and time
  return `${formattedDate} ${formattedTime}`;
}