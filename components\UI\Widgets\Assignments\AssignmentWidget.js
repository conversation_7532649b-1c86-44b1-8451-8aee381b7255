import styles from "../../../WidgetDashboard/WidgetDashboard.module.css";

export const AssignmentWidget = ({ gradeColor, data }) => {

  const formatDate = (date) => {
    const dueDate = new Date(date);
    if (isNaN(dueDate)) return ""; // Return empty if invalid date

    const day = String(dueDate.getDate()).padStart(2, '0');
    const month = String(dueDate.getMonth() + 1).padStart(2, '0'); // months are 0-based
    const year = String(dueDate.getFullYear()).slice(-2); // Get last two digits of the year

    return `${month}/${day}/${year}`;
  };

  return (
    <div className={styles.gradesContainer}>
      <div className={styles.tableWrapper}>
        <table className={styles.widgetTable}>
          <thead className={styles.dataTableHeader}>
            <tr>
              <th className={styles.tableHeader}>Class Name</th>
              <th className={styles.tableHeader}>Assignment</th>
              <th className={styles.tableHeader}>Due Date</th>
            </tr>
          </thead>
          <tbody>
            {data && data.map((item, index) => (
              <tr key={index}>
                <td className={styles.tableData}>{item.className}</td>
                <td className={styles.tableData}>{item.assignment}</td>
                <td className={styles.tableData} style={{ color: gradeColor, textAlign: 'center' }}>
                  {formatDate(item.dueDate)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}