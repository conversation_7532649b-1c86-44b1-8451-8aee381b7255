import React from "react";
import { ScheduleWidget } from "../ClassScedule/ClassSchedule";

export function MyCSN  ({filteredData}) {
  return (
    <>
      <div>
        Choose your course-load in PeopleSoft for the Fall 2024 semester, Due: 04/15/2024
      </div>
      <div style={{display:'flex', flexDirection:'column', gap:'10px'}}>
        {filteredData?.professorData.map((professor, index) => (
          <div key={index}>
            <ScheduleWidget
              subject={professor.cr07c_coursename}
              courseId={professor.cr07c_courseid}
              dateTime={professor.cr07c_classschedule}
              location={professor.cr07c_campus + " Campus"}
              building={professor.cr07c_building}
              room={professor.cr07c_classroomlocation}
              displayBuilding={false}
              classCode={professor.cr07c_classcode}
              section={professor.cr07c_section}
            />
            {/* <div style={{fontWeight:'600'}}>Assigned Teacher Assistants:</div>
            {professor.cr07c_ta1name && <div>{professor.cr07c_ta1name} - {professor.cr07c_ta1email}</div>}
            {professor.cr07c_ta2name && <div>{professor.cr07c_ta2name} - {professor.cr07c_ta2email}</div>} */}
          </div>
        ))}
      </div>
    </>
  );
}