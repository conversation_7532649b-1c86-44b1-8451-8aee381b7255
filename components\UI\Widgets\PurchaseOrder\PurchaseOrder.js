import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";

export const PurchaseOrder = ({}) => {
  const mockData = [
    { 
      id: 98895,
      title: 'External Hard Drive 5TB',
      totalAmount: 345.78,
      department: 'Registrar',
      lastUpdated: '7/21/2024',   
    },
    { 
      id: 98896,
      title: 'Office Desk',
      totalAmount: 250.00,
      department: 'Human Resources',
      lastUpdated: '7/22/2024',   
    },
    { 
      id: 98897,
      title: 'Laptop Stand',
      totalAmount: 45.99,
      department: 'IT',
      lastUpdated: '7/23/2024',   
    },
    { 
      id: 98898,
      title: 'Projector Screen',
      totalAmount: 150.75,
      department: 'Training',
      lastUpdated: '7/24/2024',   
    },
    { 
      id: 98899,
      title: 'Conference Table',
      totalAmount: 1200.00,
      department: 'Sales',
      lastUpdated: '7/25/2024',   
    },
    { 
      id: 98900,
      title: 'Marketing Brochures',
      totalAmount: 500.00,
      department: 'Marketing',
      lastUpdated: '7/26/2024',   
    },
    { 
      id: 98901,
      title: 'Research Journals',
      totalAmount: 300.00,
      department: 'Research',
      lastUpdated: '7/27/2024',   
    },
  ];
  
  return (
    <DataTable
      value={mockData}
      lazy
      columnResizeMode="expand"
      dataKey="id"
      paginator
      first={0}
      rows={10}
      globalFilterFields={[]}
      selectionMode="single"
      >
      <Column
        field="id"
        header="Req ID"
      />
      <Column
        field="title"
        header="Title"
      />
      <Column
        field="totalAmount"
        header="Total Amount"
      />
      <Column
        field="department"
        header="Department"
      />
      <Column
        field="lastUpdated"
        header="Last Updated"
      />
    </DataTable>
  );
}