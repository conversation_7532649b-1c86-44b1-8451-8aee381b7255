import React, { useState, useEffect } from "react"  
import { Tile, TileContainer } from "../../Tile/Tile"  
import { Tab, TabContainer } from "../../Tabs/Tabs"  
import { DataTable } from "primereact/datatable"  
import { Column } from "primereact/column"  
// Icons
import Waiting from "../../../../public/Icons/Waiting.svg"  
import Hold from "../../../../public/Icons/Hold.svg"  
import Approved from "../../../../public/Icons/Approved.svg"  
import Rejected from "../../../../public/Icons/Rejected.svg"  
// Hooks
import useUtilityFunctions from "../../../../hooks/useUtilityFunctions"  

export const RoutingApprovals = () => {
  const { createDashboardDate } = useUtilityFunctions()  
  const headerStyle = { fontWeight: "600", fontSize: "15.5px", color: "#000" }  
  const tabMapper = {
    InProgress: ["Waiting For Me", "On Hold"],
    Approved: ["Approved"],
    Rejected: ["Rejected"],
  }  

  const iconMapper = {
    "Waiting For Me": Waiting,
    "On Hold": Hold,
    Approved: Approved,
    Rejected: Rejected,
  }  

  const mockDataMapper = {
    "Waiting For Me": [
      {
        formSubmissionId: 123,
        formDefinition: { name: "Software Licenses" },
        submittedAtUtc: "2024-07-29T10:00:00Z",
        lastUpdatedAtUtc: "2024-07-29T10:00:00Z",
      },
      {
        formSubmissionId: 125,
        formDefinition: { name: "New Hire Equipment" },
        submittedAtUtc: "2024-07-30T11:00:00Z",
        lastUpdatedAtUtc: "2024-07-30T11:00:00Z",
      },
    ],
    "On Hold": [
      {
        formSubmissionId: 122,
        formDefinition: { name: "Hardware Licenses" },
        submittedAtUtc: "2024-07-29T10:00:00Z",
        lastUpdatedAtUtc: "2024-07-29T10:00:00Z",
      },
      {
        formSubmissionId: 126,
        formDefinition: { name: "Office Furniture Request" },
        submittedAtUtc: "2024-07-31T12:00:00Z",
        lastUpdatedAtUtc: "2024-07-31T12:00:00Z",
      },
    ],
    Approved: [
      {
        formSubmissionId: 124,
        formDefinition: { name: "Routing Form" },
        submittedAtUtc: "2024-07-29T10:00:00Z",
        lastUpdatedAtUtc: "2024-07-29T10:00:00Z",
      },
      {
        formSubmissionId: 127,
        formDefinition: { name: "Budget Approval" },
        submittedAtUtc: "2024-08-01T09:00:00Z",
        lastUpdatedAtUtc: "2024-08-01T09:00:00Z",
      },
    ],
    Rejected: [
      {
        formSubmissionId: 121,
        formDefinition: { name: "Routing Form" },
        submittedAtUtc: "2024-07-29T10:00:00Z",
        lastUpdatedAtUtc: "2024-07-29T10:00:00Z",
      },
      {
        formSubmissionId: 128,
        formDefinition: { name: "Travel Request" },
        submittedAtUtc: "2024-08-02T13:00:00Z",
        lastUpdatedAtUtc: "2024-08-02T13:00:00Z",
      },
    ],
  };

  const submittedAtBodyTemplate = (formSubmission) => {
    const { submittedAtUtc } = formSubmission  
    return createDashboardDate(submittedAtUtc)  
  }  

  const lastUpdatedBodyTemplate = (formSubmission) => {
    const { lastUpdatedAtUtc } = formSubmission  
    return createDashboardDate(lastUpdatedAtUtc)  
  }  

  const [selectedTile, setSelectedTile] = useState("InProgress")  
  const [selectedTab, setSelectedTab] = useState(tabMapper["InProgress"][0])  

  const handleTileClick = (tile) => {
    setSelectedTile(tile)  
    setSelectedTab(tabMapper[tile][0])  
  }  

  const handleTabClick = (tab) => {
    setSelectedTab(tab)  
  }  

  return (
    <>
      <TileContainer>
        <Tile
          title="Waiting For Me"
          colorOption="InProgress"
          number={4}
          handleClick={() => handleTileClick("InProgress")}
          isActive={selectedTile === "InProgress"}
        />
        <Tile
          title="Approved"
          colorOption="Approved"
          number={2}
          handleClick={() => handleTileClick("Approved")}
          isActive={selectedTile === "Approved"}
        />
        <Tile
          title="Rejected"
          colorOption="Rejected"
          number={2}
          handleClick={() => handleTileClick("Rejected")}
          isActive={selectedTile === "Rejected"}
        />
      </TileContainer>
      <TabContainer>
        {tabMapper[selectedTile].map((tab) => (
          <Tab
            key={tab}
            title={tab}
            isActive={selectedTab === tab}
            handleClick={() => handleTabClick(tab)}
            display={true}
            icon={iconMapper[tab]}
          />
        ))}
      </TabContainer>
      <DataTable
        value={mockDataMapper[selectedTab]}
        lazy
        columnResizeMode="expand"
        dataKey="formSubmissionId"
        paginator
        first={0}
        rows={10}
        totalRecords={mockDataMapper[selectedTab].length}
        loading={false}
        globalFilterFields={[]}
        selectionMode="single"
        onSelectionChange={(e) => console.log(e.value)}
      >
        <Column
          className="dashboardTitle"
          field="formSubmissionId"
          header="Form ID"
          sortable
          headerStyle={{ ...headerStyle, width: "7%" }}
        />
        <Column
          className="dashboardTitle"
          field="formDefinition.name"
          header="Form Title"
          sortable
          headerStyle={{ ...headerStyle, width: "10%" }}
        />
        <Column
          className="dashboardTitle"
          field="submittedAtUtc"
          header="Date Submitted"
          body={submittedAtBodyTemplate}
          sortable
          headerStyle={{ ...headerStyle, width: "11%" }}
        />
        <Column
          className="dashboardTitle"
          field="lastUpdatedAtUtc"
          header="Last Updated"
          body={lastUpdatedBodyTemplate}
          sortable
          headerStyle={{ ...headerStyle, width: "10%" }}
        />
      </DataTable>
    </>
  )  
}  
