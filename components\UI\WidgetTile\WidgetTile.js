import Image from 'next/image'
import styles from './WidgetTile.module.css'
import { Button } from 'primereact/button'
import { ConditionalDisplay } from '../../ConditionalDisplay/ConditionalDisplay'

export const WidgetTile = ({
  children,
  image,
  label,
  titleImage,
  width = 100,
  height = 680,
  header,
  imageWidth = 25,
  imageHeight = 25,
  titleImageWidth = 100,
  titleImageHeight = 30,
  italics = false,
  actionButton
}) => {
  return (
    <div className="widgetContainer" style={{ width: `${width}%`, height: `${height}px` }}>
      <div className={styles.widgetName}>
        <ConditionalDisplay condition={image}>
          <Image src={image} alt="" width={imageWidth} height={imageHeight} />
        </ConditionalDisplay>
        <ConditionalDisplay condition={titleImage}>
          <Image src={titleImage} alt="" width={titleImageWidth} height={titleImageHeight} className={styles.titleImage} />
        </ConditionalDisplay>
        <ConditionalDisplay condition={!titleImage}>
          <label className={`${styles.widgetLabel} ${italics ? styles.italics : ''}`}>{label}</label>
        </ConditionalDisplay>
      </div>
      <div className="headerContainer">
        <div className="widgetHeader">{header}</div>
        <ConditionalDisplay condition={actionButton}>
          <Button
            label={actionButton?.label}
            className="secondary-button-color"
            style={{ width: "150px", height: "45px", fontSize: "14px" }}
            onClick={() => window.open(actionButton.url, '_blank')}
          />
        </ConditionalDisplay>
      </div>
      <div className={styles.childrenContainer}>
        {children}
      </div>
    </div>
  )
}
