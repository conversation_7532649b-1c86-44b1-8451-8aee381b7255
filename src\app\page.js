import { PublicClientApplication } from "@azure/msal-browser";
import { PortalPageContent } from "../../components/UI/Page/PortalPageContent";
import { query } from "../../components/api/connection";
import { msalConfig } from "../../utilities/Msal/msalConfig";
import axios from "axios";
export const msalInstance = new PublicClientApplication(msalConfig);
export const dynamic = "force-dynamic";

const api = process.env.NEXT_PUBLIC_FORM_BUILDER_API_DEMO;


export default async function Home() {
  try {
    //Query the database for all required data
    const [
      users,
      peoplesoftRecord,
      appointmentRecord,
      transferHireDate,
      workdayRecord,
      absenseRecord,
      holidayCalenderRecord,
    ] = await Promise.all([
      query(
        `SELECT class_name, section, title, due_at, pending, submitted 
         FROM "INT_canvas_assignment_dim" 
         ORDER BY due_at ASC`
      ),
      query(`SELECT * FROM class`),
      query(
        `SELECT 
           TO_CHAR( g.start, 'YYYY-MM-DD HH24:MI:SS') as start, 
           TO_CHAR( g.end, 'YYYY-MM-DD HH24:MI:SS') as end, 
           g.reason, 
           g.employee_id,
           g.appointmentLink,
           s.full_legal_name,
           s.student_id
         FROM 
           student s
         INNER JOIN 
           appointment g
         ON 
           s.student_id = g.student_id
         WHERE 
           g.employee_id IN (4, 30)
         ORDER BY g.start ASC`
      ),
      query(
        `SELECT employee_id, eff_transfer_hire_date 
          FROM "INT_workday_employee" 
          WHERE employee_id IN (4, 30)`
      ),
      query(`SELECT * FROM workday_widget`),
      query(
        `SELECT 
           wa.employee_id, 
           wa.start_date, 
           wa.end_date, 
           wa.approved_by,
           CONCAT(e.pref_name_first_name, ' ', e.pref_name_lastname) AS approvername
         FROM 
           workday_absense wa
         JOIN 
           "INT_workday_employee" e
         ON wa.approved_by = e.employee_id`
      ),
      query(`SELECT name, TO_CHAR(date, 'YYYY-MM-DD') as date, type FROM special_dates`),
    ]);

    // Log the data for debugging purposes
    console.log({
      users,
      peoplesoftRecord,
      appointmentRecord,
      transferHireDate,
      workdayRecord,
      absenseRecord,
      holidayCalenderRecord,
    });

    return (
      <PortalPageContent
        dbData={users}
        peoplesoftData={peoplesoftRecord}
        appointmentData={appointmentRecord}
        transferHireData={transferHireDate}
        workdayData={workdayRecord}
        absenseData={absenseRecord}
        holidayCalenderData={holidayCalenderRecord}
      />
    );
  } catch (error) {
    console.error("Error during queries:", error);
    return <div>There was an error loading the data.</div>;
  }


}
