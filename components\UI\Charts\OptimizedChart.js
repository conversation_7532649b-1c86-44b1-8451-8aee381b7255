import React, { lazy, Suspense } from 'react';

// Lazy load chart components to reduce initial bundle size
const LazyDoughnut = lazy(() => 
  import('react-chartjs-2').then(module => ({ default: module.Doughnut }))
);

const LazyLine = lazy(() => 
  import('react-chartjs-2').then(module => ({ default: module.Line }))
);

// Chart.js registration - only import what we need
import {
  Chart as ChartJS,
  ArcElement,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

// Register only the components we actually use
ChartJS.register(
  ArcElement,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

// Loading component
const ChartLoading = () => (
  <div className="flex items-center justify-center h-48">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
  </div>
);

// Optimized Doughnut Chart
export const OptimizedDoughnut = ({ data, options, ...props }) => (
  <Suspense fallback={<ChartLoading />}>
    <LazyDoughnut data={data} options={options} {...props} />
  </Suspense>
);

// Optimized Line Chart
export const OptimizedLine = ({ data, options, ...props }) => (
  <Suspense fallback={<ChartLoading />}>
    <LazyLine data={data} options={options} {...props} />
  </Suspense>
);

// Default export for backward compatibility
export default {
  Doughnut: OptimizedDoughnut,
  Line: OptimizedLine,
};
