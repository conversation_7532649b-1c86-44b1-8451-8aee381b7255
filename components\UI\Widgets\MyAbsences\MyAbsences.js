import React from "react";
import { formatDate } from "../../../../utilities/DateTimeConverter/DateTimeConverter";

export const MyAbsences = ({ filteredData, dataverseData }) => {
  console.log('dataverseData', dataverseData);

  const pendingApproval = (
    <div>
      <div style={{ color: 'red' }}>Pending Approval:</div>
    </div>
  );

  const sortedAndFilteredStaffData = filteredData?.staffData || [];
  const teamStaffData = dataverseData?.staffData
    .filter(staff => staff.cr07c_nextptostartdate && staff.cr07c_nextptoenddate)
    .sort((a, b) => {
      const dateA = new Date(a.cr07c_nextptostartdate).getTime();
      const dateB = new Date(b.cr07c_nextptostartdate).getTime();
      return dateA - dateB;
    });

  const finalStaffData = sortedAndFilteredStaffData.length > 0 ? sortedAndFilteredStaffData : teamStaffData;
  const showName = !filteredData && dataverseData;

  const upcomingPTOElements = finalStaffData?.map((staff, index, array) => (
    <div key={index}>
      {showName && (
        <div style={{ fontSize: '20px', fontWeight: '600' }}>{staff.cr07c_name}</div>
      )}
      <div style={{ fontSize: '20px' }}>
        {formatDate(staff.cr07c_nextptostartdate)} - {formatDate(staff.cr07c_nextptoenddate)}
      </div>
      <div style={{ display: 'flex' }}>
        <div style={{ fontWeight: 'bold', paddingRight: '5px' }}>{index === array.length - 1 ? pendingApproval : "Approved By:"}  </div>
        <div style={{ display: 'flex', gap: "10px" }}>
          {staff.cr07c_supervisorname}
        </div>
      </div>
    </div>
  ));

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
      {upcomingPTOElements}
    </div>
  );
};
