import moment from "moment/moment";
import timeZone from "moment-timezone";

const useUtilityFunctions = () => {
  // const createUserFriendlyId = async (id, formType) => {
  //   const combinedString = formType + id;

  //   const CryptoJS = (await import("crypto-js")).default;
  //   const hash = CryptoJS.SHA256(combinedString);

  //   const hashString = hash.toString();

  //   const alphanumericValue = hashString.substring(0, 6).toUpperCase();

  //   const formTypeInitials = (type) => {
  //     const words = type.split(/[\s\-]/);
  //     // firstTwoWords checks if words has only one word or multiple. Without this check, an error is thrown if words.length is
  //     // equal to one since words[1] becomes undefined.
  //     const firstTwoWords =
  //       words.length === 1 ? words : Array(words[0], words[1]);
  //     const acronym = firstTwoWords.map((word) => word.charAt(0)).join("");
  //     return acronym.toUpperCase();
  //   };

  //   return formTypeInitials(formType) + "-" + alphanumericValue;
  // };

  const createUserFriendlyDate = (oldDate) => {
    const newDate = new Date(oldDate);
    return `${moment(newDate).format("DD-MMM-YYYY, HH:mm:ss A")} ${timeZone
      .tz(Intl.DateTimeFormat().resolvedOptions().timeZone)
      .format("z")}`;
  };

  const toDateObject = (date) => {
    if (typeof date !== "string" && !isNaN(Date.parse(date))) {
      return new Date(date);
    } else if (date instanceof Date && !isNaN(date)) {
      return date;
    } else if (typeof date === "string") {
      return new Date(date);
    } else {
      console.log("Unknown date format for toDateObject ", date);
      return null;
    }
  };

  const createDashboardDate = (oldDate) => {
    const newDate = new Date(oldDate);
    return `${moment(newDate).format("MM/DD/YYYY")}`;
  };

  const createUserFriendlyStatus = (formDefinitionStatus) => {
    return formDefinitionStatus === 0 ? "Active" : "Disabled";
  };

  const createISODate = (oldDate) => {
    const date = new Date(oldDate);
    return `${date.getFullYear()}-${(date.getMonth() + 1)
      .toString()
      .padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")}`;
  };

  const extractTextValueRegex = (str) => {
    const match = str.match(/"text":"\s*(.*?)\s*"/);
    return match ? match[1] : "";
  };

  const createTargetObject = (name, val) => {
    return { target: { name: name, value: val } };
  };

  const formatDateToMMDDYYYY = (inputDate) => {
    const date = new Date(inputDate);

    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const year = date.getFullYear();

    return `${month}/${day}/${year}`;
  };

  const formatTimeToHHMMAMPM = (inputDate) => {
    const date = new Date(inputDate);

    let hours = date.getHours();
    const minutes = String(date.getMinutes()).padStart(2, "0");

    const isPM = hours >= 12;
    const ampm = isPM ? "PM" : "AM";

    hours = hours % 12;
    hours = hours ? hours : 12;

    return `${String(hours).padStart(2, "0")}:${minutes} ${ampm}`;
  };

  const currencyFormat = (value, decimal = 2, symbol) => {
    const val = parseFloat(value);
    if (val) {
      // new Intl.NumberFormat(undefined, { style: "currency", currency: "USD" }).format(number)
      return `${symbol ?? ""}${new Intl.NumberFormat("en-us", {
        maximumFractionDigits: decimal,
        minimumFractionDigits: decimal,
      })
        .format(val)
        .padEnd(2, "0")}`;
    }
    return `${symbol ?? ""}${new Intl.NumberFormat("en-us", {
      maximumFractionDigits: decimal,
      minimumFractionDigits: decimal,
    })
      .format(0)
      .padEnd(2, "0")}`;
  };

  const replaceUnderscoreWithSpace = (inputString) => {
    return inputString.replace(/_/g, " ");
  };

  const getFormattedTime = () => {
    const newDate = new Date();
    const hours = newDate.getHours();
    const minutes = newDate.getMinutes();
    const ampm = hours >= 12 ? "PM" : "AM";

    // Add leading zero for single-digit hours or minutes
    const formattedHours = String(hours).padStart(2, "0");
    const formattedMinutes = String(minutes).padStart(2, "0");

    return `${formattedHours}:${formattedMinutes} ${ampm}`;
  };

  const formattedTimeZone = () => {
    const userTimeZone = moment.tz.guess();
    const todayDate = moment.tz(new Date(), userTimeZone);
    const offsetString = todayDate.utcOffset();
    const currentTime = getFormattedTime();

    return `Time Zone: ${replaceUnderscoreWithSpace(userTimeZone)} (GMT: ${
      offsetString >= 0 ? "+" : ""
    }${offsetString / 60}:00) ${currentTime}`;
  };

  return {
    // createUserFriendlyId,
    toDateObject,
    createTargetObject,
    createUserFriendlyDate,
    createDashboardDate,
    createUserFriendlyStatus,
    extractTextValueRegex,
    formatDateToMMDDYYYY,
    formatTimeToHHMMAMPM,
    createISODate,
    currencyFormat,
    formattedTimeZone,
  };
};

export default useUtilityFunctions;
