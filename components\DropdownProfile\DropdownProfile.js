import React, { useState, useRef, useEffect } from "react";
import styles from "./DropdownProfile.module.css";
import Image from "next/image";
import { Button } from "primereact/button";
import CommonDP from '../../public/NewIcons/newDp_blue.svg'

export const DropdownProfile = ({ userProfile, handleLogout, userImage }) => {
  const [isVisible, setIsVisible] = useState(false);
  const dropdownRef = useRef(null);

  const toggleDropdown = () => setIsVisible(!isVisible);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsVisible(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [dropdownRef]);

  return (
    <div className={styles.dropdownContainer} ref={dropdownRef}>
      <Image src={userImage ?? CommonDP} width={140} height={140} alt="Avatar" onClick={toggleDropdown} className={styles.avatar} />
      {isVisible && (
        <div className={styles.dropdownContent}>
          <div className={styles.blueBar}>
            <Image style={{ marginTop: '25px' }} src={userImage} alt="Avatar Logo" width={140} height={140} className={styles.avatarDrop} />
          </div>
          <h3>Account</h3>
          <div className={styles.name}>{userProfile?.displayName}</div>
          <div className={styles.email}>{userProfile?.email}</div>
          <Button className="secondary-button-color" style={{ width: '200px' }} label="Sign Out" onClick={handleLogout} />
        </div>
      )}
    </div>
  );
};





