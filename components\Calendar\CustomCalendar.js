import React, { useState } from 'react';
import styles from './CustomCalendar.module.css';

export const CustomCalendar = ({ onChange }) => {
  const [selectedDate, setSelectedDate] = useState('');

  const handleDateChange = (e) => {
    setSelectedDate(e.target.value);
    if (onChange) {
      onChange(e.target.value);
    }
  };

  return (
    <div className={styles.calendar}>
      <input
        type="date"
        value={selectedDate}
        onChange={handleDateChange}
        className={styles.datePicker}
      />
    </div>
  );
};

