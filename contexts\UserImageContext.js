"use client";
import React, { useState, useEffect, createContext } from "react";
import { useMsal, useAccount } from "@azure/msal-react";
import { loginRequest } from "../utilities/Msal/msalConfig";
import { getAccessTokenForScope } from "../utilities/Msal/GetAccessTokenForScope";
import CommonDP from "../public/NewIcons/newDp_blue.svg";

const UserImageContext = createContext({});

const UserImageContextProvider = ({ children }) => {
  const { accounts } = useMsal();
  const account = useAccount(accounts[0] ?? {});
  const { username } = accounts.length > 0 ? accounts[0] : {};

  const [contextData, setContextData] = useState({});

  useEffect(() => {
    const loadUserProfileData = async () => {
      try {
        const accessToken = await getAccessTokenForScope(loginRequest);
        const response = await fetch(
          "https://graph.microsoft.com/v1.0/me/photo/$value",
          {
            method: "GET",
            headers: {
              Accept: "*/*",
              Authorization: `Bearer ${accessToken}`,
            },
            "next-action": "graph.microsoft.com",
          }
        );

        if (!response.ok) {
          throw new Error(`Error: ${response.status}`);
        }

        const data = await response.blob();
        const imageUrl = URL.createObjectURL(data);
        setContextData({ imageUrl });
      } catch (error) {
        console.error(error);
        const defaultImageUrl = CommonDP;
        setContextData({ imageUrl: defaultImageUrl });
      }
    };

    const eraseUserProfileData = () => {
      setContextData({});
    };

    if (account) {
      loadUserProfileData();
    } else if (account === null) {
      eraseUserProfileData();
    }
  }, [account, username]);

  return (
    <UserImageContext.Provider value={contextData}>
      {children}
    </UserImageContext.Provider>
  );
};

export { UserImageContext, UserImageContextProvider };
