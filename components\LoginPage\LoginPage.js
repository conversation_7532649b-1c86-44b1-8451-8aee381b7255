import React from "react";
import Image from "next/image";
import {SignInButton} from "../../components/SignInButton";
import styles from "./LoginPage.module.css";
import CSNLogo from "../../public/SSOImages/CSNLogo.png";
import SSOLogo from "../../public/SSOImages/SSOLogo.webp";
import BackgroundImage from "../../public/SSOImages/BackgroundImage.png";
import Password from "../../public/SSOImages/Password.svg";
import Portal from "../../public/SSOImages/Portal.svg";
import LoginImage from "../../public/SSOImages/Login_Backgroundimage.svg";
import Illustration from "../../public/SSOImages/Login_illus.svg";
import {Button} from "primereact/button";
import {InputText} from "primereact/inputtext";
import clsx from "clsx";
import {getDynamicLogo} from "../../utilities/LogoMap/logoMapping";

export default function LoginPage() {
  return (
    <div className={styles.backgroundSignIn}>
      <Image
        src={BackgroundImage}
        layout="fill"
        objectFit="cover"
        objectPosition="center"
        alt="Background"
      />
      <div className={styles.backgroundBlur}>
        <div className="bg-color-glass-white flex justify-center items-center p-0 lg:p-0 my-0 lg:mx-0 w-[100%] h-[100%] rounded-[0px] lg:rounded-[0px]">
          <div className="bg-white w-full h-full rounded-[0px] flex">
            <div
              className={clsx(
                styles.leftContainer,
                "hidden lg:flex justify-center h-full w-1/2"
              )}
            >
              <div className="ml-0 lg:flex flex-col justify-center p-5 gap-10">
                <div className="flex">
                  <Image
                    src={Illustration}
                    alt="Illustration"
                    width={380}
                    height={110}
                  />
                </div>
                <div className="font-georgia text-3xl text-color-navy-blue flex justify-center text-center self-center w-[100%]">
                  {process.env.NEXT_PUBLIC_UNIVERSITY_NAME}
                  <br /> empowers our students and
                  <br />
                  communities to achieve,
                  <br /> succeed and prosper.
                </div>
              </div>
            </div>
            <div className="flex flex-col relative justify-center items-center w-full lg:w-1/2 h-full rounded-[0px] p-1.5">
              <div className="absolute w-[96%] md:w-[98%] lg:w-[100%] h-[100%] rounded-[0px] p-0 left-2 lg:left-0 top-0">
                <Image
                  src={LoginImage}
                  alt="Login Image"
                  layout="fill"
                  objectFit="cover"
                  className="rounded-[0px] py-0"
                />
              </div>
              {/* when uncommen the username password height want to set auto*/}
              <div className="absolute flex flex-col rounded-[0px] p-5 z-[1] w-[90%] max-w-md h-[40%] top-[50%] left-[50%] bg-color-frosted-white backdrop-blur-xl transform translate-x-[-50%] translate-y-[-50%]">
                {/* when uncommen the username password height want to set auto <div className="h-full">  */}
                <div className="h-full flex flex-col justify-center gap-10">
                  {/* when uncommen the username password height want to set auto  <div className="flex justify-center w-full my-16"> */}
                  <div className="flex justify-center w-full">
                    <Image
                      src={getDynamicLogo()}
                      alt="CSN Logo"
                      width={250}
                      height={56}
                    />
                  </div>
                  {/* <div className="flex flex-col gap-12 mt-16">
                    <div className="flex mx-0 lg:mx-5 my-0 pb-1 border-b border-b-color-dark-red">
                      <Image src={Portal} alt="Portal" />
                      <InputText
                        placeholder="Username"
                        type="text"
                        className="w-full ml-5 text-base lg:text-lg text-color-navy-blue font-open-sans border-0 focus:shadow-none outline-0 bg-transparent"
                      />
                    </div>
                    <div className="flex mx-0 lg:mx-5 my-0 pb-1 border-b border-b-color-dark-red">
                      <Image src={Password} alt="Password" />
                      <InputText
                        placeholder="Password"
                        type="password"
                        className="w-full ml-5 text-base lg:text-lg text-color-navy-blue font-open-sans border-0 focus:shadow-none outline-0 bg-transparent"
                      />
                    </div>
                  </div>
                  <div className="flex text-sm lg:text-base justify-end text-white font-open-sans underline mr-4 mt-2 cursor-pointer">
                    Forget Password?
                  </div> */}
                  {/* when uncommen the username password height want to set auto <div className="flex justify-center my-32"> */}
                  <div className="flex justify-center">
                    <SignInButton />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
