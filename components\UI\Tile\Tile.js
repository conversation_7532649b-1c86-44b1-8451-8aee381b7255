import React from "react";
import styles from "./Tile.module.css";
import clsx from "clsx";

export const TileContainer = ({ children, display = true }) => {
  return display ? (
    <div
      style={{
        width: "100%",
        display: "flex",
        flexDirection: "row",
        justifyContent: "space-between",
        gap: "6px"
      }}
    >
      {children}
    </div>
  ) : (
    <></>
  );
};

export const Tile = ({
  title,
  colorOption,
  isActive,
  handleClick,
  width = "100%",
  number = 0,
  showIcon,
  style,
  titleFontSize,
  numberFontWeight,
  numberFontSize,
  display = true,
}) => {
  const tileStyle = {
    height: style,
  };
  const SINGLE_DIGIT = 1;

  const tileColorMapper = {
    InProgress: {
      tileBackgroundColor: styles.inProgressBackgroundColor,
      numberBackgroundColor: styles.inProgressNumberBackgroundColor,
      activeColor: styles.inProgressActiveStyle,
    },
    Approved: {
      tileBackgroundColor: styles.approvedBackgroundColor,
      numberBackgroundColor: styles.approvedNumberBackgroundColor,
      activeColor: styles.approvedActiveColor,
    },
    Rejected: {
      tileBackgroundColor: styles.rejectedBackgroundColor,
      numberBackgroundColor: styles.rejectedNumberBackgroundColor,
      activeColor: styles.rejectedActiveColor,
    },
    Finalized: {
      tileBackgroundColor: styles.finalizedBackgroundColor,
      numberBackgroundColor: styles.finalizedNumberBackgroundColor,
      activeColor: styles.finalizedActiveColor,
    },
    TotalFinalized: {
      tileBackgroundColor: styles.totalFinalizedBackgroundColor,
      numberBackgroundColor: styles.totalFinalizedNumberBackgroundColor,
      activeColor: styles.totalFinalizedActiveColor,
    },
    Pending: {
      tileBackgroundColor: styles.pendingBackgroundColor,
      numberBackgroundColor: styles.pendingNumberBackgroundColor,
      activeColor: styles.pendingActiveColor,
    },
  };

  return display ? (
    <>
      <div
        className={`${styles.tile} ${tileColorMapper[colorOption]?.tileBackgroundColor
          } ${isActive && tileColorMapper[colorOption].activeColor}`}
        style={{ ...tileStyle, width: width }}
        onClick={handleClick}
      >
        <div className={styles.tileBody}>
          <div
            className={clsx(
              styles.tileNumber,
              number.toString().length === SINGLE_DIGIT &&
              styles.tileNumber_singleDigit,
              tileColorMapper[colorOption]?.numberBackgroundColor
            )}
            style={{ fontWeight: numberFontWeight, fontSize: numberFontSize }}
          >
            {number ? number : 0}
          </div>
          <div className={styles.tileTextContainer}>
            <div
              className={styles.tileTitle}
              style={{ fontSize: titleFontSize }}
            >
              {title}
            </div>
          </div>
        </div>
      </div>
    </>
  ) : (
    <></>
  );
};