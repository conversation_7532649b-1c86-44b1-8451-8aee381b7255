import clsx from "clsx"
import Image from "next/image"
import { ConditionalDisplay } from "../../ConditionalDisplay/ConditionalDisplay"

import styles from "./Tabs.module.css"


export const TabContainer = ({ justifyContent, children }) => {
  return (
    <div className={styles.tabContainer} style={justifyContent}>
      {children}
    </div>
  )
}

export const Tab = ({ title, value, key, isActive, handleClick, display, icon }) => {
  return (
    <ConditionalDisplay condition={display}>
      <div key={key} className={clsx(styles.tab, isActive && styles.active)} onClick={(e) => handleClick(e, value)} >
        <ConditionalDisplay condition={typeof icon === 'string'}>
          <span className={`${icon} ${styles.icon}`} />  
        </ConditionalDisplay>
        <ConditionalDisplay condition={typeof icon !== 'string'}>
          <Image src={icon} className={styles.icon} alt="Image" />
        </ConditionalDisplay>
        <span>{title}</span>
      </div>
    </ConditionalDisplay>
  )
}
