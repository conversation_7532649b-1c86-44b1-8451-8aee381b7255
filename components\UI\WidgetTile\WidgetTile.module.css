/* .widgetContainer {
  width: 30%;
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 10px 20px;
  border-radius: 0px;
  background-color: #fff;
  border: 0.5px solid #00489078; 
} */
/* background: rgba(255, 255, 255, 0.62);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px); */

.widgetName {
  display: flex;
  gap: 10px;
  /* padding: 10px 0px; */
  align-items: center;
}

.widgetLabel {
  font-size: 26px;
  font-weight: 700;
  color: #004990;
  margin-bottom: 10px;
  margin-top: 10px;
}

/* .headerContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 10px;
  border-bottom: 1px solid #004890;
  margin-top: -10px;
} */

/* .widgetHeader {
  font-size: 20px;
  font-weight: 700;
  color: #004890;
  border-bottom: none;
  margin-top: 0;
  padding-bottom: 0;

  @media (max-width: 768px) {
    font-size: 16px;
  }
} */

.childrenContainer {
  padding: 0px;
  overflow-y: scroll;
  overflow-x: clip;
  color: #004890;
  scrollbar-width: thin;
  scrollbar-color: #55555533 #f5f5f500;
}

.childrenContainer::-webkit-scrollbar {
  width: 10px;
}

.italics {
  font-style: italic;
}

.titleImage {
  margin: 10px 0;
  object-fit: contain;
}