.tabContainer {
    display: flex;
    align-items: center;
    background-color: var(--primary-bg-darkCerulean-10);
    width: 100%;
    margin: 10px 0px 10px 0px;
    /* Replace w/ 20px 0px */
    border-radius: 5px;
}

.tab {
    flex: 0 0 auto;
    /* This prevents the tab from growing */
    position: relative;
    padding: 12px 20px;
    font-weight: 700;
    font-size: 18px;
    min-width: 150px;
    min-height: 52px;
    cursor: pointer;
    border-radius: 5px;
    color: #004890;
    background-color: var(--primary-bg-darkCerulean-20);
    transition: background-color 0.2s ease-in;
    border: none;
    /* background: transparent; */
    outline: none;
    cursor: pointer;
    text-align: center;
    display: flex;
    align-items: baseline;

    @media(max-width:768px) {
        min-width: 170px;
        font-size: 16px;
        padding-top: 1rem;
    }
}

.tab:hover {
    background-color: var(--secondary-bg-deepBlueSky);
    color: white;
}

.tab.active {
    background-color: var(--secondary-bg-deepBlueSky);
    color: white;
}

.icon {
    margin-right: 10px;
    position: relative;
    top: 3px;
}