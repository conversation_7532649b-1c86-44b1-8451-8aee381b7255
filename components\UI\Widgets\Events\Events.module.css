.eventContainer {
  margin-top: 10px;
  background-color: #0048900f;
  border-radius: 0px;
  padding: 12px;
  font-size: 14px;
}

.viewLink {
  color: var(--primaryColor);
  font-size: 18px;
  font-weight: 600;
  text-decoration: underline;
  display: flex;
  justify-content: flex-end;
  cursor: pointer;
  margin-top: 2rem;
}

.mainContainer {
  display: flex;
  flex-direction: column;
}

.dateHeader {
  font-size: 18px;
  font-weight: 600;
  color: var(--primaryColor);
  border-bottom: 1px solid var(--primaryColor);
  padding-bottom: 10px;
  margin-bottom: 10px;
}

.appointmentContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.leftContainer {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 5px;
}

.eventTime {
  font-size: 14px;
  color: var(--secondaryColor);
}

.eventDetails {
  padding: 5px 0;
}

.appointmentDay {
  background-color: #e0f7fa;
  border-radius: 50%;
}